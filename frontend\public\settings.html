<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings | Project Ruby</title>
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Ruby theme -->
    <link rel="stylesheet" href="css/ruby-core.css">
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="ruby-theme">
    <div class="container">
        <header>
            <div>

                <h1>Settings</h1>
                <div class="nav">
                    <a href="index.html">Home</a>
                    <a href="dashboard.html">Dashboard</a>
                    <a href="trading-hub.html">Trading</a>
                    <a href="charts.html">Charts</a>
                    <a href="predictions.html">Predictions</a>
                    <a href="chatbot.html">Assistant</a>
                    <a href="settings.html" style="font-weight: bold;">Settings</a>
                </div>
            </div>
            <div class="user-info">
                <span>Welcome, Demo User</span>
                <span style="color: var(--ruby-gold); font-weight: 600;">Premium Plan</span>
                <button class="btn btn-red" onclick="alert('Logout functionality would be implemented here')">Logout</button>
            </div>
        </header>

        <div class="card">
            <h2>Account Settings</h2>

            <form class="settings-form">
                <div class="form-group">
                    <label for="display-name">Display Name</label>
                    <input type="text" id="display-name" value="Demo User">
                </div>
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" value="********">
                </div>
                <button type="submit" class="btn btn-red">Save Account Settings</button>
            </form>
        </div>

        <div class="card">
            <h2>Notification Settings</h2>
            <form class="settings-form">
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> Email Notifications
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> Browser Notifications
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox"> Mobile Notifications
                    </label>
                </div>
                <button type="submit" class="btn btn-red">Save Notification Settings</button>
            </form>
        </div>

        <div class="card">
            <h2>Display Settings</h2>
            <form class="settings-form">
                <div class="form-group">
                    <label for="theme">Theme</label>
                    <select id="theme">
                        <option value="light">Light</option>
                        <option value="dark" selected>Dark</option>
                        <option value="system">System Default</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="chart-type">Default Chart Type</label>
                    <select id="chart-type">
                        <option value="candlestick" selected>Candlestick</option>
                        <option value="line">Line</option>
                        <option value="bar">Bar</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-red">Save Display Settings</button>
            </form>
        </div>

        <div class="card">
            <h2>Data Refresh Intervals</h2>
            <form class="settings-form">
                <div class="form-group">
                    <label for="price-refresh">Price Data (seconds)</label>
                    <input type="number" id="price-refresh" value="30" min="5" max="300">
                </div>
                <div class="form-group">
                    <label for="signal-refresh">Trading Signals (seconds)</label>
                    <input type="number" id="signal-refresh" value="60" min="30" max="600">
                </div>
                <div class="form-group">
                    <label for="governance-refresh">Market Signals Data (seconds)</label>
                    <input type="number" id="governance-refresh" value="1800" min="300" max="3600">
                </div>
                <button type="submit" class="btn btn-red">Save Refresh Settings</button>
            </form>
        </div>

        <div class="last-updated">
            Last updated: <span id="last-updated-time">2023-04-21 14:30:00</span>
        </div>
    </div>
</body>
</html>
