/**
 * Streamlined Navigation System for Project Ruby
 * Single source of truth for navigation across all pages
 */

class StreamlinedNavigation {
    constructor() {
        this.pages = [
            { id: 'trading-hub', name: '🚀 Trading Hub', url: 'trading-hub.html', primary: true },
            { id: 'assistant', name: '🤖 Assistant', url: 'assistant.html', primary: true },
            { id: 'dashboard', name: 'Dashboard', url: 'dashboard.html', primary: true },
            { id: 'charts', name: 'Charts', url: 'charts.html', primary: false },
            { id: 'predictions', name: 'Predictions', url: 'predictions.html', primary: false },
            { id: 'settings', name: 'Settings', url: 'settings.html', primary: false }
        ];

        this.currentPage = this.getCurrentPage();
        this.init();
    }

    getCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop() || 'index.html';
        return filename.replace('.html', '');
    }

    init() {
        this.updateNavigation();
        this.setupEventListeners();
    }

    updateNavigation() {
        // Find all navigation containers
        const navContainers = document.querySelectorAll('.nav, .navigation, nav');

        navContainers.forEach(nav => {
            this.renderNavigation(nav);
        });
    }

    renderNavigation(container) {
        // Clear existing navigation
        container.innerHTML = '';

        // Add primary pages
        this.pages.filter(page => page.primary).forEach(page => {
            const link = this.createNavLink(page);
            container.appendChild(link);
        });

        // Add secondary pages in a dropdown or separate section
        const secondaryPages = this.pages.filter(page => !page.primary);
        if (secondaryPages.length > 0) {
            const moreDropdown = this.createMoreDropdown(secondaryPages);
            container.appendChild(moreDropdown);
        }
    }

    createNavLink(page) {
        const link = document.createElement('a');
        link.href = page.url;
        link.textContent = page.name;
        link.className = 'nav-link';

        // Highlight current page
        if (this.currentPage === page.id ||
            (this.currentPage === 'index' && page.id === 'trading-hub')) {
            link.style.fontWeight = 'bold';
            link.style.color = '#d4af37';
        }

        return link;
    }

    createMoreDropdown(pages) {
        const dropdown = document.createElement('div');
        dropdown.className = 'nav-dropdown';
        dropdown.style.position = 'relative';
        dropdown.style.display = 'inline-block';

        const trigger = document.createElement('a');
        trigger.href = '#';
        trigger.textContent = 'More ▼';
        trigger.className = 'nav-link dropdown-trigger';
        trigger.style.cursor = 'pointer';

        const menu = document.createElement('div');
        menu.className = 'dropdown-menu';
        menu.style.cssText = `
            display: none;
            position: absolute;
            top: 100%;
            right: 0;
            background: rgba(0,0,0,0.9);
            border: 1px solid rgba(212,175,55,0.3);
            border-radius: 8px;
            padding: 10px 0;
            min-width: 150px;
            z-index: 1000;
        `;

        pages.forEach(page => {
            const link = document.createElement('a');
            link.href = page.url;
            link.textContent = page.name;
            link.style.cssText = `
                display: block;
                padding: 8px 15px;
                color: #fff;
                text-decoration: none;
                transition: background 0.2s;
            `;
            link.addEventListener('mouseenter', () => {
                link.style.background = 'rgba(212,175,55,0.2)';
            });
            link.addEventListener('mouseleave', () => {
                link.style.background = 'transparent';
            });
            menu.appendChild(link);
        });

        // Toggle dropdown
        trigger.addEventListener('click', (e) => {
            e.preventDefault();
            menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!dropdown.contains(e.target)) {
                menu.style.display = 'none';
            }
        });

        dropdown.appendChild(trigger);
        dropdown.appendChild(menu);

        return dropdown;
    }

    setupEventListeners() {
        // Add keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        window.location.href = 'trading-hub.html';
                        break;
                    case '2':
                        e.preventDefault();
                        window.location.href = 'assistant.html';
                        break;
                    case '3':
                        e.preventDefault();
                        window.location.href = 'dashboard.html';
                        break;
                    case '4':
                        e.preventDefault();
                        window.location.href = 'charts.html';
                        break;
                }
            }
        });
    }

    // Public API for adding custom navigation items
    addPage(page) {
        this.pages.push(page);
        this.updateNavigation();
    }

    // Remove a page from navigation
    removePage(pageId) {
        this.pages = this.pages.filter(page => page.id !== pageId);
        this.updateNavigation();
    }

    // Get navigation stats
    getStats() {
        return {
            totalPages: this.pages.length,
            primaryPages: this.pages.filter(p => p.primary).length,
            secondaryPages: this.pages.filter(p => !p.primary).length,
            currentPage: this.currentPage
        };
    }
}

// Initialize navigation when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.streamlinedNav = new StreamlinedNavigation();

    // Add navigation styles
    const style = document.createElement('style');
    style.textContent = `
        .nav-link {
            color: #fff;
            text-decoration: none;
            padding: 8px 16px;
            margin: 0 5px;
            border-radius: 4px;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .nav-link:hover {
            color: #d4af37;
            background: rgba(212, 175, 55, 0.1);
        }

        .nav {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        @media (max-width: 768px) {
            .nav {
                flex-direction: column;
                align-items: stretch;
            }

            .nav-link {
                text-align: center;
                margin: 2px 0;
            }
        }
    `;
    document.head.appendChild(style);

    console.log('🧭 Streamlined Navigation initialized');
    console.log('📊 Navigation stats:', window.streamlinedNav.getStats());
});

// Export for use in other scripts
window.StreamlinedNavigation = StreamlinedNavigation;
