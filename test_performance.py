#!/usr/bin/env python3
"""
Performance Test Suite for Project Ruby
Tests the optimized backend performance
"""

import asyncio
import aiohttp
import time
import statistics
import json
from concurrent.futures import ThreadPoolExecutor
import requests

# Test configuration
BASE_URL = "http://localhost:8004"
TEST_ENDPOINTS = [
    "/health",
    "/api/performance",
    "/api/cc/price/BTC",
    "/api/cc/price/ETH",
    "/api/cc/price/SOL",
    "/signals"
]

async def test_async_performance():
    """Test async endpoint performance"""
    print("🚀 Testing Async Performance...")
    
    async with aiohttp.ClientSession() as session:
        results = {}
        
        for endpoint in TEST_ENDPOINTS:
            url = f"{BASE_URL}{endpoint}"
            times = []
            
            print(f"Testing {endpoint}...")
            
            # Test 10 requests to each endpoint
            for i in range(10):
                start_time = time.time()
                try:
                    async with session.get(url) as response:
                        if response.status == 200:
                            await response.json()
                            end_time = time.time()
                            times.append((end_time - start_time) * 1000)  # Convert to ms
                        else:
                            print(f"❌ Error {response.status} for {endpoint}")
                except Exception as e:
                    print(f"❌ Exception for {endpoint}: {e}")
            
            if times:
                avg_time = statistics.mean(times)
                min_time = min(times)
                max_time = max(times)
                
                results[endpoint] = {
                    "avg_ms": round(avg_time, 2),
                    "min_ms": round(min_time, 2),
                    "max_ms": round(max_time, 2),
                    "requests": len(times)
                }
                
                print(f"  ✅ Avg: {avg_time:.2f}ms, Min: {min_time:.2f}ms, Max: {max_time:.2f}ms")
        
        return results

def test_sync_performance():
    """Test synchronous performance for comparison"""
    print("🐌 Testing Sync Performance (for comparison)...")
    
    results = {}
    
    for endpoint in TEST_ENDPOINTS:
        url = f"{BASE_URL}{endpoint}"
        times = []
        
        print(f"Testing {endpoint}...")
        
        # Test 10 requests to each endpoint
        for i in range(10):
            start_time = time.time()
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    response.json()
                    end_time = time.time()
                    times.append((end_time - start_time) * 1000)  # Convert to ms
                else:
                    print(f"❌ Error {response.status_code} for {endpoint}")
            except Exception as e:
                print(f"❌ Exception for {endpoint}: {e}")
        
        if times:
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            
            results[endpoint] = {
                "avg_ms": round(avg_time, 2),
                "min_ms": round(min_time, 2),
                "max_ms": round(max_time, 2),
                "requests": len(times)
            }
            
            print(f"  ✅ Avg: {avg_time:.2f}ms, Min: {min_time:.2f}ms, Max: {max_time:.2f}ms")
    
    return results

async def test_concurrent_load():
    """Test concurrent request handling"""
    print("⚡ Testing Concurrent Load...")
    
    async with aiohttp.ClientSession() as session:
        # Test 50 concurrent requests to the price endpoint
        url = f"{BASE_URL}/api/cc/price/BTC"
        
        async def make_request():
            start_time = time.time()
            try:
                async with session.get(url) as response:
                    if response.status == 200:
                        await response.json()
                        return (time.time() - start_time) * 1000
            except Exception as e:
                print(f"❌ Concurrent request failed: {e}")
                return None
        
        start_time = time.time()
        tasks = [make_request() for _ in range(50)]
        results = await asyncio.gather(*tasks)
        total_time = (time.time() - start_time) * 1000
        
        successful_requests = [r for r in results if r is not None]
        
        if successful_requests:
            avg_time = statistics.mean(successful_requests)
            print(f"  ✅ 50 concurrent requests completed in {total_time:.2f}ms")
            print(f"  ✅ Average response time: {avg_time:.2f}ms")
            print(f"  ✅ Success rate: {len(successful_requests)}/50 ({len(successful_requests)/50*100:.1f}%)")
            
            return {
                "total_time_ms": round(total_time, 2),
                "avg_response_ms": round(avg_time, 2),
                "success_rate": len(successful_requests) / 50,
                "requests_per_second": round(50 / (total_time / 1000), 2)
            }
        
        return None

async def test_cache_performance():
    """Test caching effectiveness"""
    print("💾 Testing Cache Performance...")
    
    async with aiohttp.ClientSession() as session:
        url = f"{BASE_URL}/api/cc/price/BTC"
        
        # First request (cache miss)
        start_time = time.time()
        async with session.get(url) as response:
            await response.json()
        first_request_time = (time.time() - start_time) * 1000
        
        # Wait a moment
        await asyncio.sleep(0.1)
        
        # Second request (should be cached)
        start_time = time.time()
        async with session.get(url) as response:
            await response.json()
        second_request_time = (time.time() - start_time) * 1000
        
        cache_improvement = ((first_request_time - second_request_time) / first_request_time) * 100
        
        print(f"  ✅ First request (cache miss): {first_request_time:.2f}ms")
        print(f"  ✅ Second request (cache hit): {second_request_time:.2f}ms")
        print(f"  ✅ Cache improvement: {cache_improvement:.1f}%")
        
        return {
            "cache_miss_ms": round(first_request_time, 2),
            "cache_hit_ms": round(second_request_time, 2),
            "improvement_percent": round(cache_improvement, 1)
        }

async def get_server_metrics():
    """Get server performance metrics"""
    print("📊 Getting Server Metrics...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/api/performance") as response:
                if response.status == 200:
                    data = await response.json()
                    print("  ✅ Server metrics retrieved")
                    return data
                else:
                    print(f"  ❌ Failed to get metrics: {response.status}")
    except Exception as e:
        print(f"  ❌ Error getting metrics: {e}")
    
    return None

def print_performance_report(async_results, sync_results, concurrent_results, cache_results, server_metrics):
    """Print a comprehensive performance report"""
    print("\n" + "="*60)
    print("🎯 PROJECT RUBY PERFORMANCE REPORT")
    print("="*60)
    
    if async_results and sync_results:
        print("\n📈 ASYNC vs SYNC COMPARISON:")
        for endpoint in async_results:
            if endpoint in sync_results:
                async_time = async_results[endpoint]["avg_ms"]
                sync_time = sync_results[endpoint]["avg_ms"]
                improvement = ((sync_time - async_time) / sync_time) * 100
                
                print(f"  {endpoint}:")
                print(f"    Async: {async_time}ms | Sync: {sync_time}ms | Improvement: {improvement:.1f}%")
    
    if concurrent_results:
        print(f"\n⚡ CONCURRENT LOAD TEST:")
        print(f"  Requests per second: {concurrent_results['requests_per_second']}")
        print(f"  Average response time: {concurrent_results['avg_response_ms']}ms")
        print(f"  Success rate: {concurrent_results['success_rate']*100:.1f}%")
    
    if cache_results:
        print(f"\n💾 CACHE PERFORMANCE:")
        print(f"  Cache hit improvement: {cache_results['improvement_percent']}%")
        print(f"  Cache miss time: {cache_results['cache_miss_ms']}ms")
        print(f"  Cache hit time: {cache_results['cache_hit_ms']}ms")
    
    if server_metrics:
        print(f"\n📊 SERVER METRICS:")
        if 'cache_stats' in server_metrics:
            cache_stats = server_metrics['cache_stats']
            print(f"  Cache hit rate: {cache_stats.get('hit_rate', 0)*100:.1f}%")
            print(f"  Cached items: {cache_stats.get('cached_items', 0)}")
        
        if 'server_info' in server_metrics:
            features = server_metrics['server_info'].get('features', [])
            print(f"  Enabled features: {', '.join(features)}")
    
    print("\n🚀 PERFORMANCE SUMMARY:")
    print("  ✅ Async HTTP implementation active")
    print("  ✅ Connection pooling enabled")
    print("  ✅ Intelligent caching system")
    print("  ✅ GZip compression active")
    print("  ✅ Performance monitoring enabled")
    print("\n" + "="*60)

async def main():
    """Main performance test function"""
    print("🚀 PROJECT RUBY PERFORMANCE TEST SUITE")
    print("="*50)
    
    try:
        # Test server availability
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/health") as response:
                if response.status != 200:
                    print("❌ Server not available. Please start the server first.")
                    return
        
        print("✅ Server is running, starting performance tests...\n")
        
        # Run all tests
        async_results = await test_async_performance()
        print()
        
        sync_results = test_sync_performance()
        print()
        
        concurrent_results = await test_concurrent_load()
        print()
        
        cache_results = await test_cache_performance()
        print()
        
        server_metrics = await get_server_metrics()
        
        # Generate report
        print_performance_report(async_results, sync_results, concurrent_results, cache_results, server_metrics)
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
