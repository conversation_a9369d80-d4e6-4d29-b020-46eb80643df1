/**
 * High-Performance Service Worker for Project Ruby
 * Provides aggressive caching and offline capabilities
 */

const CACHE_NAME = 'project-ruby-v2.0.0';
const API_CACHE_NAME = 'project-ruby-api-v2.0.0';

// Resources to cache immediately
const STATIC_RESOURCES = [
    '/',
    '/dashboard.html',
    '/trading.html',
    '/charts.html',
    '/predictions.html',
    '/css/ruby-core.css',
    '/js/performance-optimizer.js',
    '/js/data-service.js',
    '/js/market-data-api.js',
    '/js/coin-config.js'
];

// API endpoints to cache with different strategies
const API_CACHE_STRATEGIES = {
    '/api/cc/price/': { strategy: 'networkFirst', maxAge: 30000 }, // 30 seconds
    '/signals': { strategy: 'cacheFirst', maxAge: 60000 }, // 1 minute
    '/api/performance': { strategy: 'networkOnly', maxAge: 0 }, // Always fresh
    '/health': { strategy: 'networkFirst', maxAge: 10000 } // 10 seconds
};

// Install event - cache static resources
self.addEventListener('install', event => {
    console.log('Service Worker installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('Caching static resources...');
                return cache.addAll(STATIC_RESOURCES);
            })
            .then(() => {
                console.log('Static resources cached successfully');
                return self.skipWaiting();
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME && cacheName !== API_CACHE_NAME) {
                            console.log('Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', event => {
    const url = new URL(event.request.url);
    
    // Handle API requests
    if (url.hostname === 'localhost' && url.port === '8004') {
        event.respondWith(handleApiRequest(event.request));
        return;
    }
    
    // Handle static resources
    if (event.request.method === 'GET') {
        event.respondWith(handleStaticRequest(event.request));
    }
});

// Handle API requests with smart caching
async function handleApiRequest(request) {
    const url = new URL(request.url);
    const pathname = url.pathname;
    
    // Find matching cache strategy
    let strategy = { strategy: 'networkFirst', maxAge: 60000 }; // Default
    for (const [pattern, config] of Object.entries(API_CACHE_STRATEGIES)) {
        if (pathname.includes(pattern)) {
            strategy = config;
            break;
        }
    }
    
    const cache = await caches.open(API_CACHE_NAME);
    
    switch (strategy.strategy) {
        case 'cacheFirst':
            return cacheFirst(request, cache, strategy.maxAge);
        case 'networkFirst':
            return networkFirst(request, cache, strategy.maxAge);
        case 'networkOnly':
            return networkOnly(request);
        default:
            return networkFirst(request, cache, strategy.maxAge);
    }
}

// Handle static resource requests
async function handleStaticRequest(request) {
    const cache = await caches.open(CACHE_NAME);
    
    // Try cache first for static resources
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
        return cachedResponse;
    }
    
    // Fallback to network
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.error('Network request failed:', error);
        
        // Return offline fallback if available
        if (request.destination === 'document') {
            return cache.match('/dashboard.html');
        }
        
        throw error;
    }
}

// Cache-first strategy
async function cacheFirst(request, cache, maxAge) {
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
        const cacheDate = new Date(cachedResponse.headers.get('date') || 0);
        const age = Date.now() - cacheDate.getTime();
        
        if (age < maxAge) {
            return cachedResponse;
        }
    }
    
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const responseToCache = networkResponse.clone();
            responseToCache.headers.set('date', new Date().toISOString());
            cache.put(request, responseToCache);
        }
        return networkResponse;
    } catch (error) {
        if (cachedResponse) {
            return cachedResponse;
        }
        throw error;
    }
}

// Network-first strategy
async function networkFirst(request, cache, maxAge) {
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const responseToCache = networkResponse.clone();
            responseToCache.headers.set('date', new Date().toISOString());
            cache.put(request, responseToCache);
        }
        return networkResponse;
    } catch (error) {
        const cachedResponse = await cache.match(request);
        if (cachedResponse) {
            const cacheDate = new Date(cachedResponse.headers.get('date') || 0);
            const age = Date.now() - cacheDate.getTime();
            
            // Return cached response even if expired when network fails
            if (age < maxAge * 2) { // Allow 2x maxAge for offline scenarios
                return cachedResponse;
            }
        }
        throw error;
    }
}

// Network-only strategy
async function networkOnly(request) {
    return fetch(request);
}

// Background sync for failed requests
self.addEventListener('sync', event => {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

async function doBackgroundSync() {
    console.log('Performing background sync...');
    
    // Retry failed API requests
    const cache = await caches.open(API_CACHE_NAME);
    const requests = await cache.keys();
    
    for (const request of requests) {
        try {
            const response = await fetch(request);
            if (response.ok) {
                cache.put(request, response.clone());
            }
        } catch (error) {
            console.log('Background sync failed for:', request.url);
        }
    }
}

// Push notifications for real-time updates
self.addEventListener('push', event => {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body,
            icon: '/images/icon-192x192.png',
            badge: '/images/badge-72x72.png',
            tag: 'trading-signal',
            renotify: true,
            actions: [
                {
                    action: 'view',
                    title: 'View Signal'
                },
                {
                    action: 'dismiss',
                    title: 'Dismiss'
                }
            ]
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

// Handle notification clicks
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    if (event.action === 'view') {
        event.waitUntil(
            clients.openWindow('/trading.html')
        );
    }
});

// Performance monitoring
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'GET_CACHE_STATS') {
        getCacheStats().then(stats => {
            event.ports[0].postMessage(stats);
        });
    }
});

async function getCacheStats() {
    const staticCache = await caches.open(CACHE_NAME);
    const apiCache = await caches.open(API_CACHE_NAME);
    
    const staticKeys = await staticCache.keys();
    const apiKeys = await apiCache.keys();
    
    return {
        staticCacheSize: staticKeys.length,
        apiCacheSize: apiKeys.length,
        totalCacheSize: staticKeys.length + apiKeys.length,
        cacheNames: [CACHE_NAME, API_CACHE_NAME]
    };
}

console.log('🚀 High-Performance Service Worker loaded - offline support enabled!');
