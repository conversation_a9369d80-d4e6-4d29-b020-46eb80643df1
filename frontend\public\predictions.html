<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Price Predictions | Project Ruby</title>
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Ruby theme -->
    <link rel="stylesheet" href="css/ruby-core.css">
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Original styles (preserved for functionality) -->
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .nav {
            display: flex;
            gap: 20px;
        }
        .nav a {
            color: #0066cc;
            text-decoration: none;
        }
        .nav a:hover {
            text-decoration: underline;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-red {
            background-color: #f44336;
            color: white;
        }
    </style>
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/data-service.js"></script>
    <script src="js/coin-config.js"></script>
    <script src="js/coin-sync.js"></script>
    <script src="js/add-coin-sync.js"></script>
    <script src="js/market-data-api.js"></script>
    <script src="js/price-prediction.js"></script>
    <script src="js/update-navigation.js"></script>
    <script src="js/system-status.js"></script>
    <script src="js/system-initializer.js"></script>
    <script src="js/startup.js"></script>
    <style>
        /* Auto-refresh animation */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .auto-refresh-indicator i {
            margin-right: 5px;
        }

        .auto-refresh-indicator.refreshing i {
            animation: spin 1s linear infinite;
        }

        /* Enhanced Prediction Page Styles */
        .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .grid-container > div {
            transition: transform 0.2s, box-shadow 0.2s;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .grid-container > div:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        }

        .prediction-settings {
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .prediction-settings h2 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
        }

        .prediction-settings h2 i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        .settings-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .form-group label i {
            margin-right: 5px;
            color: var(--primary-color);
        }

        .form-group select,
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-group select:focus,
        .form-group input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
            outline: none;
        }

        .form-actions {
            grid-column: span 2;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .btn-apply {
            background-color: #1976d2;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            transition: background-color 0.2s, transform 0.1s;
        }

        .btn-apply i {
            margin-right: 5px;
        }

        .btn-apply:active {
            transform: scale(0.98);
        }

        .btn-apply:hover {
            background-color: #1565c0;
        }

        .btn-reset {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            transition: background-color 0.2s, transform 0.1s;
        }

        .btn-reset i {
            margin-right: 5px;
        }

        .btn-reset:hover {
            background-color: #e0e0e0;
        }

        .btn-reset:active {
            transform: scale(0.98);
        }

        .prediction-info {
            background-color: #e3f2fd;
            border-left: 4px solid #1976d2;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .prediction-info h3 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 16px;
            display: flex;
            align-items: center;
        }

        .prediction-info h3 i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        .prediction-info p {
            margin: 0;
            line-height: 1.5;
        }

        .prediction-info ul {
            margin-top: 10px;
            margin-bottom: 0;
            padding-left: 25px;
        }

        .prediction-info li {
            margin-bottom: 5px;
        }

        .prediction-info li:last-child {
            margin-bottom: 0;
        }

        /* Prediction Explanation Styles */
        .prediction-explanation-container {
            margin: 20px 0;
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 15px;
            border-left: 4px solid #1976d2;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .prediction-explanation-container h4 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #1976d2;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .prediction-explanation-container h4 i {
            margin-right: 8px;
        }

        .prediction-explanation {
            font-size: 14px;
            line-height: 1.5;
            color: #333;
        }

        .prediction-explanation p {
            margin: 8px 0;
        }

        .prediction-explanation strong {
            font-weight: 600;
            color: #1976d2;
        }

        /* Backtesting results table */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border-radius: 8px;
            overflow: hidden;
        }

        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table th {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        .data-table tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .data-table td.highlight {
            font-weight: bold;
            color: var(--primary-color);
        }

        /* Loading state */
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 300px;
        }

        .loading-text {
            margin-left: 10px;
            color: var(--text-secondary);
        }

        /* Help button and modal */
        .help-button {
            background-color: #e3f2fd;
            color: var(--primary-color);
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 14px;
            cursor: pointer;
            margin-left: 10px;
            display: inline-flex;
            align-items: center;
            transition: background-color 0.2s;
        }

        .help-button i {
            margin-right: 5px;
        }

        .help-button:hover {
            background-color: #bbdefb;
        }

        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            overflow-y: auto;
        }

        .help-modal-content {
            background-color: white;
            margin: 50px auto;
            padding: 20px;
            border-radius: 8px;
            max-width: 800px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .help-modal-close {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 24px;
            cursor: pointer;
            color: #666;
            transition: color 0.2s;
        }

        .help-modal-close:hover {
            color: #333;
        }

        .help-section {
            margin-bottom: 20px;
        }

        .help-section h3 {
            margin-top: 0;
            margin-bottom: 10px;
            color: var(--primary-color);
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }

        .help-item {
            margin-bottom: 15px;
        }

        .help-item-title {
            font-weight: bold;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }

        .help-item-title i {
            margin-right: 5px;
            color: var(--primary-color);
        }

        .help-item-description {
            margin-left: 25px;
            color: #555;
        }

        @media (max-width: 768px) {
            .grid-container {
                grid-template-columns: 1fr;
            }

            .settings-form {
                grid-template-columns: 1fr;
            }

            .form-actions {
                grid-column: 1;
            }
        }
    </style>
</head>
<body class="ruby-theme">
    <div class="container">
        <header>
            <div>

                <h1>Price Predictions</h1>
                <div class="nav">
                    <a href="index.html">Home</a>
                    <a href="dashboard.html">Dashboard</a>
                    <a href="trading-hub.html">Trading</a>
                    <a href="charts.html">Charts</a>
                    <a href="predictions.html" style="font-weight: bold;">Predictions</a>
                    <a href="chatbot.html">Assistant</a>
                    <a href="settings.html">Settings</a>
                </div>
            </div>

        </header>

        <div class="prediction-info">
            <h3><i class="fas fa-chart-line"></i> Advanced Price Predictions</h3>
            <p>
                Our sophisticated prediction system analyzes multiple data sources to generate accurate price forecasts:
            </p>
            <ul>
                <li><strong>Historical Price Data:</strong> Technical patterns, volatility, and market cycles</li>
                <li><strong>Market Sentiment:</strong> Social media analysis, news sentiment, and investor psychology</li>
                <li><strong>On-Chain Metrics:</strong> Network activity, wallet distributions, and smart contract interactions</li>
                <li><strong>Macroeconomic Factors:</strong> Inflation trends, interest rates, and global economic conditions</li>
            </ul>
            <p class="mt-1"><em>These predictions are for informational purposes only and should not be considered financial advice.</em></p>
        </div>

        <div class="prediction-info model-info">
            <h3><i class="fas fa-cogs"></i> Our Prediction Strategies</h3>
            <div class="model-grid">
                <div class="model-card">
                    <div class="model-header">
                        <i class="fas fa-tree"></i>
                        <h4>Pattern Recognition</h4>
                    </div>
                    <p>Combines multiple analytical approaches to create a robust prediction system. Each component analyzes different data subsets, making it resistant to market noise and outliers.</p>
                    <div class="model-features">
                        <span><i class="fas fa-check"></i> Excellent for Bitcoin</span>
                        <span><i class="fas fa-check"></i> Stable predictions</span>
                        <span><i class="fas fa-check"></i> Low false signals</span>
                    </div>
                </div>

                <div class="model-card">
                    <div class="model-header">
                        <i class="fas fa-chart-line"></i>
                        <h4>Adaptive Analysis</h4>
                    </div>
                    <p>Builds predictions sequentially, with each iteration correcting errors from previous ones. Excellent at identifying subtle patterns in the market.</p>
                    <div class="model-features">
                        <span><i class="fas fa-check"></i> High accuracy</span>
                        <span><i class="fas fa-check"></i> Great for short-term</span>
                        <span><i class="fas fa-check"></i> Multi-source data</span>
                    </div>
                </div>

                <div class="model-card">
                    <div class="model-header">
                        <i class="fas fa-network-wired"></i>
                        <h4>Deep Insight</h4>
                    </div>
                    <p>Advanced system designed to remember patterns over time. Adapts to changing market conditions and finds hidden relationships between different market factors.</p>
                    <div class="model-features">
                        <span><i class="fas fa-check"></i> Best for volatility</span>
                        <span><i class="fas fa-check"></i> Adapts to changes</span>
                        <span><i class="fas fa-check"></i> Complex patterns</span>
                    </div>
                </div>

                <div class="model-card featured">
                    <div class="model-header">
                        <i class="fas fa-layer-group"></i>
                        <h4>Ruby Ensemble (Recommended)</h4>
                    </div>
                    <p>Combines all three strategies using a weighted approach based on historical performance. Weights adjust dynamically based on recent accuracy metrics.</p>
                    <div class="model-features">
                        <span><i class="fas fa-check"></i> Most consistent</span>
                        <span><i class="fas fa-check"></i> All market conditions</span>
                        <span><i class="fas fa-check"></i> Balanced approach</span>
                    </div>
                </div>
            </div>
            <p class="mt-1"><em>Click the "Field Explanations" button above for more detailed information about each model.</em></p>
        </div>

        <style>
            .model-info {
                background-color: #f0f7ff;
            }

            .model-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
                gap: 15px;
                margin-top: 15px;
            }

            .model-card {
                background-color: white;
                border-radius: 8px;
                padding: 15px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                transition: transform 0.2s, box-shadow 0.2s;
                border-top: 3px solid #1976d2;
            }

            .model-card:hover {
                transform: translateY(-3px);
                box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            }

            .model-card.featured {
                border-top-color: #4caf50;
                background-color: #f9fff9;
            }

            .model-header {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
            }

            .model-header i {
                font-size: 24px;
                margin-right: 10px;
                color: #1976d2;
            }

            .model-card.featured .model-header i {
                color: #4caf50;
            }

            .model-header h4 {
                margin: 0;
                font-size: 16px;
            }

            .model-card p {
                font-size: 14px;
                color: #555;
                margin-bottom: 10px;
                line-height: 1.4;
            }

            .model-features {
                display: flex;
                flex-direction: column;
                gap: 5px;
            }

            .model-features span {
                font-size: 13px;
                color: #666;
                display: flex;
                align-items: center;
            }

            .model-features i {
                color: #4caf50;
                margin-right: 5px;
                font-size: 12px;
            }

            @media (max-width: 768px) {
                .model-grid {
                    grid-template-columns: 1fr;
                }
            }
        </style>

        <div class="card">
            <h2><i class="fas fa-cogs"></i> Prediction Settings <button id="show-help" class="help-button"><i class="fas fa-question-circle"></i> Field Explanations</button></h2>
            <div class="settings-form">
                <div class="form-group">
                    <label for="model-type"><i class="fas fa-layer-group"></i> Strategy Type</label>
                    <select id="model-type">
                        <option value="random_forest">Pattern Recognition</option>
                        <option value="gradient_boosting">Adaptive Analysis</option>
                        <option value="neural_network">Deep Insight</option>
                        <option value="ensemble">Ruby Ensemble (Recommended)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="time-horizon"><i class="fas fa-calendar-alt"></i> Time Horizon</label>
                    <select id="time-horizon">
                        <option value="1">1 day (Ultra Short Term)</option>
                        <option value="3">3 days (Very Short Term)</option>
                        <option value="7" selected>7 days (Short Term)</option>
                        <option value="14">14 days (Medium Term)</option>
                        <option value="30">30 days (Long Term)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="confidence-interval"><i class="fas fa-percentage"></i> Confidence Interval</label>
                    <select id="confidence-interval">
                        <option value="0.8">80%</option>
                        <option value="0.9" selected>90%</option>
                        <option value="0.95">95%</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="include-sentiment"><i class="fas fa-comments"></i> Include Sentiment Data</label>
                    <select id="include-sentiment">
                        <option value="true" selected>Yes</option>
                        <option value="false">No</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="data-sources"><i class="fas fa-database"></i> Data Sources</label>
                    <select id="data-sources">
                        <option value="all" selected>All Sources</option>
                        <option value="price_only">Price Data Only</option>
                        <option value="price_sentiment">Price + Sentiment</option>
                        <option value="price_onchain">Price + On-Chain</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="volatility-adjustment"><i class="fas fa-chart-line"></i> Volatility Adjustment</label>
                    <select id="volatility-adjustment">
                        <option value="auto" selected>Automatic</option>
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button class="btn-reset" id="reset-settings"><i class="fas fa-undo"></i> Reset</button>
                    <button class="btn-apply" id="apply-settings"><i class="fas fa-check"></i> Apply Settings</button>
                </div>
            </div>
        </div>

        <div class="card">
            <h2><i class="fas fa-chart-line"></i> Price Predictions</h2>
            <div id="predictions-container">
                <!-- Prediction widgets will be dynamically created here -->
                <div class="loading-container">
                    <div class="loading-spinner"></div>
                    <span class="loading-text">Loading predictions...</span>
                </div>
            </div>

            <div id="prediction-explanations">
                <!-- Prediction explanations will be dynamically created here -->
            </div>

            <div class="grid-container">
                <div id="additional-metrics">
                    <div class="card" style="height: 100%; margin: 0;">
                        <h3 style="margin-top: 0;"><i class="fas fa-tachometer-alt"></i> Prediction Metrics</h3>
                        <div class="metrics-container">
                            <div class="metric-item">
                                <div class="metric-label"><i class="fas fa-bullseye"></i> Accuracy (7-day)</div>
                                <div class="metric-value">87.3%</div>
                                <div class="metric-trend positive"><i class="fas fa-arrow-up"></i> +2.1%</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label"><i class="fas fa-chart-bar"></i> RMSE</div>
                                <div class="metric-value">2.34%</div>
                                <div class="metric-trend positive"><i class="fas fa-arrow-down"></i> -0.5%</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label"><i class="fas fa-exchange-alt"></i> Directional Accuracy</div>
                                <div class="metric-value">76.2%</div>
                                <div class="metric-trend positive"><i class="fas fa-arrow-up"></i> +1.8%</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label"><i class="fas fa-balance-scale"></i> Sharpe Ratio</div>
                                <div class="metric-value">1.87</div>
                                <div class="metric-trend positive"><i class="fas fa-arrow-up"></i> +0.23</div>
                            </div>
                        </div>
                        <div style="margin-top: 15px; font-style: italic; color: #666; font-size: 14px;">
                            <i class="fas fa-info-circle"></i> Metrics based on backtesting results from the last 90 days.
                        </div>
                    </div>
                </div>
            </div>

            <style>
                .metrics-container {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 15px;
                    margin-top: 15px;
                }

                .metric-item {
                    background-color: #f9f9f9;
                    border-radius: 6px;
                    padding: 15px;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                }

                .metric-label {
                    font-size: 14px;
                    color: #666;
                    margin-bottom: 5px;
                    display: flex;
                    align-items: center;
                }

                .metric-label i {
                    margin-right: 5px;
                    color: var(--primary-color);
                }

                .metric-value {
                    font-size: 24px;
                    font-weight: bold;
                    color: #333;
                }

                .metric-trend {
                    font-size: 14px;
                    margin-top: 5px;
                    display: flex;
                    align-items: center;
                }

                .metric-trend i {
                    margin-right: 3px;
                }

                .metric-trend.positive {
                    color: var(--success-color);
                }

                .metric-trend.negative {
                    color: var(--danger-color);
                }

                @media (max-width: 768px) {
                    .metrics-container {
                        grid-template-columns: 1fr;
                    }
                }
            </style>

        </div>

        <div class="card">
            <h2><i class="fas fa-flask"></i> Backtesting Results</h2>
            <p>Model performance based on historical data from the last 90 days:</p>

            <table class="data-table">
                <thead>
                    <tr>
                        <th><i class="fas fa-coins"></i> Asset</th>
                        <th><i class="fas fa-layer-group"></i> Strategy</th>
                        <th><i class="fas fa-chart-bar"></i> RMSE</th>
                        <th><i class="fas fa-ruler"></i> MAE</th>
                        <th><i class="fas fa-exchange-alt"></i> Directional Accuracy</th>
                        <th><i class="fas fa-square-root-alt"></i> R²</th>
                        <th><i class="fas fa-balance-scale"></i> Sharpe Ratio</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>BTC</strong></td>
                        <td>Pattern Recognition</td>
                        <td class="highlight">2.34%</td>
                        <td>1.87%</td>
                        <td class="highlight">68.5%</td>
                        <td>0.72</td>
                        <td class="highlight">1.87</td>
                    </tr>
                    <tr>
                        <td><strong>BTC</strong></td>
                        <td>Deep Insight</td>
                        <td>2.67%</td>
                        <td>2.12%</td>
                        <td>65.8%</td>
                        <td>0.68</td>
                        <td>1.65</td>
                    </tr>
                    <tr>
                        <td><strong>ETH</strong></td>
                        <td>Pattern Recognition</td>
                        <td class="highlight">3.12%</td>
                        <td>2.45%</td>
                        <td class="highlight">65.2%</td>
                        <td>0.68</td>
                        <td class="highlight">1.54</td>
                    </tr>
                    <tr>
                        <td><strong>ETH</strong></td>
                        <td>Adaptive Analysis</td>
                        <td>3.45%</td>
                        <td>2.78%</td>
                        <td>63.7%</td>
                        <td>0.65</td>
                        <td>1.42</td>
                    </tr>
                    <tr>
                        <td><strong>SOL</strong></td>
                        <td>Deep Insight</td>
                        <td class="highlight">4.23%</td>
                        <td>3.56%</td>
                        <td class="highlight">64.3%</td>
                        <td>0.63</td>
                        <td class="highlight">1.38</td>
                    </tr>
                    <tr>
                        <td><strong>SOL</strong></td>
                        <td>Pattern Recognition</td>
                        <td>4.56%</td>
                        <td>3.78%</td>
                        <td>62.1%</td>
                        <td>0.61</td>
                        <td>1.25</td>
                    </tr>
                </tbody>
            </table>

            <div style="margin-top: 15px; font-style: italic; color: #666;">
                <i class="fas fa-info-circle"></i> Highlighted cells indicate the best performing strategy for each asset. The strategy with the highest Sharpe ratio is selected as the default for predictions.
            </div>
    </div>

    <div style="text-align: center; margin-top: 20px; color: #757575; font-size: 12px;" id="last-updated"></div>

    <!-- Help Modal -->
    <div id="help-modal" class="help-modal">
        <div class="help-modal-content">
            <span class="help-modal-close">&times;</span>
            <h2>Project Ruby: Price Predictions Explained</h2>
            <p>This guide explains all the fields and metrics displayed on the Price Predictions page.</p>

            <!-- Prediction Settings Section -->
            <div class="help-section">
                <h3>Prediction Settings</h3>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-layer-group"></i> Strategy Type</div>
                    <div class="help-item-description">The prediction strategy used to generate price forecasts. Options include:
                        <ul>
                            <li><strong>Pattern Recognition:</strong> An advanced method that identifies recurring patterns in market data. This approach analyzes multiple data subsets to find reliable signals while filtering out market noise. Pattern Recognition is particularly good at handling non-linear relationships and can capture complex market patterns. It typically performs well for Bitcoin and Ethereum predictions due to its stability with high-volume assets.</li>
                            <li><strong>Adaptive Analysis:</strong> Builds predictions sequentially, with each iteration correcting errors from previous ones. This approach focuses on improving forecasts where previous attempts performed poorly. Adaptive Analysis is excellent at identifying subtle patterns and often provides higher accuracy, especially for short-term predictions. It's particularly effective when combining multiple data sources like price data, sentiment, and on-chain metrics.</li>
                            <li><strong>Deep Insight:</strong> Advanced system designed to remember patterns over time. Our implementation uses sophisticated algorithms specifically designed to identify long-term trends. Deep Insight excels at finding hidden relationships in the data and adapting to changing market conditions. It typically performs best for assets with high volatility like Solana, where traditional approaches might struggle to capture rapid market shifts.</li>
                            <li><strong>Ruby Ensemble:</strong> Combines predictions from all three strategies (Pattern Recognition, Adaptive Analysis, and Deep Insight) using a weighted approach based on historical performance. The weights adjust dynamically based on recent accuracy metrics. This approach leverages the strengths of each individual strategy while minimizing their weaknesses, resulting in more robust predictions across different market conditions. The Ruby Ensemble typically provides the most consistent performance across all assets and time horizons.</li>
                        </ul>
                    </div>
                </div>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-calendar-alt"></i> Time Horizon</div>
                    <div class="help-item-description">The future time period for which the prediction is made:
                        <ul>
                            <li><strong>1 day:</strong> Ultra short-term prediction with highest accuracy (typically 90-95% confidence), ideal for day trading</li>
                            <li><strong>3 days:</strong> Very short-term prediction with high accuracy (typically 85-90% confidence)</li>
                            <li><strong>7 days:</strong> Short-term prediction with good accuracy (typically 75-85% confidence)</li>
                            <li><strong>14 days:</strong> Medium-term prediction with moderate accuracy (typically 65-75% confidence)</li>
                            <li><strong>30 days:</strong> Long-term prediction with lower accuracy but useful for trend identification (typically 55-65% confidence)</li>
                        </ul>
                        <p>Shorter horizons typically have higher accuracy than longer horizons due to the inherent unpredictability of markets over extended periods.</p>
                    </div>
                </div>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-percentage"></i> Confidence Interval</div>
                    <div class="help-item-description">The statistical confidence level for the prediction range. A 90% confidence interval means there's a 90% probability that the actual price will fall within the predicted range.</div>
                </div>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-comments"></i> Include Sentiment Data</div>
                    <div class="help-item-description">Whether to incorporate sentiment analysis from news and social media into the prediction models. This can improve accuracy during periods of high market sentiment.</div>
                </div>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-database"></i> Data Sources</div>
                    <div class="help-item-description">The types of data used in the prediction model:
                        <ul>
                            <li><strong>All Sources:</strong> Uses price data, sentiment analysis, on-chain metrics, and economic indicators.</li>
                            <li><strong>Price Data Only:</strong> Uses only historical price and volume data.</li>
                            <li><strong>Price + Sentiment:</strong> Combines price data with sentiment analysis.</li>
                            <li><strong>Price + On-Chain:</strong> Combines price data with blockchain metrics.</li>
                        </ul>
                    </div>
                </div>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-chart-line"></i> Volatility Adjustment</div>
                    <div class="help-item-description">How the model adjusts for market volatility:
                        <ul>
                            <li><strong>Automatic:</strong> The model automatically adjusts based on current market conditions.</li>
                            <li><strong>Low:</strong> Assumes lower volatility, resulting in narrower prediction ranges.</li>
                            <li><strong>Medium:</strong> Uses standard volatility assumptions.</li>
                            <li><strong>High:</strong> Assumes higher volatility, resulting in wider prediction ranges.</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Price Prediction Charts Section -->
            <div class="help-section">
                <h3>Price Prediction Charts</h3>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-chart-line"></i> Price Line</div>
                    <div class="help-item-description">The solid line shows historical prices up to the current date.</div>
                </div>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-chart-line"></i> Prediction Line</div>
                    <div class="help-item-description">The dashed line shows the predicted price trajectory for the selected time horizon.</div>
                </div>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-chart-area"></i> Confidence Band</div>
                    <div class="help-item-description">The shaded area around the prediction line represents the confidence interval. Wider bands indicate higher uncertainty.</div>
                </div>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-arrows-alt-v"></i> Support/Resistance Levels</div>
                    <div class="help-item-description">Horizontal lines indicating key price levels where the asset has historically found support (buying interest) or resistance (selling pressure).</div>
                </div>
            </div>

            <!-- Prediction Metrics Section -->
            <div class="help-section">
                <h3>Prediction Metrics</h3>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-bullseye"></i> Accuracy</div>
                    <div class="help-item-description">The percentage of past predictions that fell within the predicted range. Higher percentages indicate more reliable predictions.</div>
                </div>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-chart-bar"></i> RMSE (Root Mean Square Error)</div>
                    <div class="help-item-description">A measure of the differences between predicted values and actual observed values. Lower values indicate better accuracy. Expressed as a percentage of the asset price.</div>
                </div>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-exchange-alt"></i> Directional Accuracy</div>
                    <div class="help-item-description">The percentage of times the model correctly predicted the direction of price movement (up or down), regardless of the magnitude.</div>
                </div>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-balance-scale"></i> Sharpe Ratio</div>
                    <div class="help-item-description">A measure of risk-adjusted return. Higher values indicate better performance relative to risk. A Sharpe ratio above 1.0 is generally considered good.</div>
                </div>
            </div>

            <!-- Backtesting Results Section -->
            <div class="help-section">
                <h3>Backtesting Results</h3>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-coins"></i> Asset</div>
                    <div class="help-item-description">The cryptocurrency being analyzed.</div>
                </div>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-layer-group"></i> Strategy</div>
                    <div class="help-item-description">The prediction strategy used for the forecast.</div>
                </div>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-chart-bar"></i> RMSE</div>
                    <div class="help-item-description">Root Mean Square Error, a measure of prediction accuracy. Lower values indicate better performance.</div>
                </div>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-ruler"></i> MAE</div>
                    <div class="help-item-description">Mean Absolute Error, another measure of prediction accuracy that is less sensitive to outliers than RMSE.</div>
                </div>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-exchange-alt"></i> Directional Accuracy</div>
                    <div class="help-item-description">The percentage of times the model correctly predicted the price direction.</div>
                </div>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-square-root-alt"></i> R²</div>
                    <div class="help-item-description">R-squared, a statistical measure that represents the proportion of the variance in the dependent variable that is predictable from the independent variables. Values closer to 1 indicate better fit.</div>
                </div>

                <div class="help-item">
                    <div class="help-item-title"><i class="fas fa-balance-scale"></i> Sharpe Ratio</div>
                    <div class="help-item-description">A measure of risk-adjusted return. Higher values indicate better performance relative to risk.</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add last updated text
        const lastUpdatedElement = document.getElementById('last-updated');
        if (lastUpdatedElement) {
            const now = new Date();
            lastUpdatedElement.textContent = `Last updated: ${now.toLocaleString()}`;
        }

        // Help modal functionality
        const helpModal = document.getElementById('help-modal');
        const helpButton = document.getElementById('show-help');
        const closeButton = document.querySelector('.help-modal-close');

        if (helpButton && helpModal) {
            helpButton.addEventListener('click', function() {
                helpModal.style.display = 'block';
                document.body.style.overflow = 'hidden'; // Prevent scrolling behind modal
            });
        }

        if (closeButton && helpModal) {
            closeButton.addEventListener('click', function() {
                helpModal.style.display = 'none';
                document.body.style.overflow = ''; // Restore scrolling
            });
        }

        // Close modal when clicking outside of it
        window.addEventListener('click', function(event) {
            if (event.target === helpModal) {
                helpModal.style.display = 'none';
                document.body.style.overflow = ''; // Restore scrolling
            }
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Create prediction widgets for all active coins
            createPredictionWidgets();

            // Listen for coin configuration changes
            window.addEventListener('storage', function(event) {
                if (event.key === 'ruby_coins') {
                    console.log('Coin configuration changed, recreating prediction widgets');
                    createPredictionWidgets();
                }
            });

            // Listen for update-predictions event from CoinSyncSystem
            document.addEventListener('update-predictions', function(event) {
                console.log('Received update-predictions event:', event.detail);
                if (event.detail && event.detail.activeCoins) {
                    createPredictionWidgets(event.detail.activeCoins);
                } else {
                    createPredictionWidgets();
                }
            });

            /**
             * Create prediction widgets for all active coins
             * @param {Array} [coinsFromEvent] - Optional array of active coins from an event
             */
            function createPredictionWidgets(coinsFromEvent) {
                // Get active coins
                let activeCoins = [];

                // If coins were provided from an event, use those
                if (coinsFromEvent && Array.isArray(coinsFromEvent)) {
                    activeCoins = coinsFromEvent;
                    console.log('Using active coins from event:', activeCoins);
                }
                // Otherwise try to get them from CoinSyncSystem
                else if (typeof CoinSyncSystem !== 'undefined' && Array.isArray(CoinSyncSystem.activeCoins)) {
                    activeCoins = CoinSyncSystem.activeCoins;
                    console.log('Using active coins from CoinSyncSystem:', activeCoins);
                }
                // Otherwise try to get them from CoinConfigService
                else if (typeof CoinConfigService !== 'undefined') {
                    activeCoins = CoinConfigService.getActiveCoins();
                    console.log('Using active coins from CoinConfigService:', activeCoins);
                }
                // Fallback to default coins if no other source is available
                else {
                    activeCoins = [
                        { symbol: 'BTC', name: 'Bitcoin' },
                        { symbol: 'ETH', name: 'Ethereum' },
                        { symbol: 'SOL', name: 'Solana' }
                    ];
                    console.log('No coin service available, using default coins');
                }

                // Get container elements
                const predictionsContainer = document.getElementById('predictions-container');
                const explanationsContainer = document.getElementById('prediction-explanations');

                // Clear containers
                predictionsContainer.innerHTML = '';
                explanationsContainer.innerHTML = '';

                // Create a grid container for the predictions
                const gridContainer = document.createElement('div');
                gridContainer.className = 'grid-container';
                predictionsContainer.appendChild(gridContainer);

                // Create widgets for each active coin
                const widgets = [];

                activeCoins.forEach((coin, index) => {
                    // Create container for this coin's prediction
                    const predictionContainer = document.createElement('div');
                    predictionContainer.id = `${coin.symbol.toLowerCase()}-prediction`;
                    predictionContainer.innerHTML = `
                        <div class="loading-container">
                            <div class="loading-spinner"></div>
                            <span class="loading-text">Loading ${coin.symbol} predictions...</span>
                        </div>
                    `;
                    gridContainer.appendChild(predictionContainer);

                    // Create explanation container for this coin
                    const explanationContainer = document.createElement('div');
                    explanationContainer.className = 'prediction-explanation-container';
                    explanationContainer.id = `${coin.symbol.toLowerCase()}-explanation`;
                    explanationContainer.innerHTML = `
                        <h4><i class="fas fa-lightbulb"></i> ${coin.symbol} Prediction Analysis</h4>
                        <div class="prediction-explanation">
                            <p>Loading analysis for ${coin.name}...</p>
                        </div>
                    `;
                    explanationsContainer.appendChild(explanationContainer);

                    // Create prediction widget
                    const modelTypes = ['random_forest', 'gradient_boosting', 'neural_network', 'ensemble'];
                    const randomModelType = modelTypes[index % modelTypes.length];

                    const widget = new PricePredictionWidget(predictionContainer.id, {
                        symbol: coin.symbol,
                        days: 7,
                        refreshInterval: 120, // Auto-refresh every 2 minutes
                        modelType: randomModelType,
                        confidenceInterval: 0.9,
                        includeSentiment: true
                    });

                    widgets.push(widget);

                    // Generate a random prediction explanation
                    const changePercent = (Math.random() * 10).toFixed(1);
                    const isPositive = Math.random() > 0.3; // 70% chance of positive prediction
                    const direction = isPositive ? 'increase' : 'decrease';

                    setTimeout(() => {
                        const explanationElement = document.querySelector(`#${coin.symbol.toLowerCase()}-explanation .prediction-explanation`);
                        if (explanationElement) {
                            explanationElement.innerHTML = `
                                <p>The ${widget.options.modelType === 'random_forest' ? 'Pattern Recognition' :
                                   widget.options.modelType === 'gradient_boosting' ? 'Adaptive Analysis' :
                                   widget.options.modelType === 'neural_network' ? 'Deep Insight' : 'Ruby Ensemble'}
                                   strategy predicts a ${changePercent}% ${direction} for ${coin.symbol} over the next week,
                                   based on ${widget.options.modelType === 'random_forest' ? 'historical price patterns, trading volume, and market sentiment' :
                                   widget.options.modelType === 'gradient_boosting' ? 'sequential analysis of market data and correction of previous errors' :
                                   widget.options.modelType === 'neural_network' ? 'complex market patterns, sentiment analysis, and technical indicators' :
                                   'a combination of multiple analytical approaches and weighted historical performance'}.</p>
                                <p>${coin.name} has shown ${isPositive ? 'positive momentum' : 'some volatility'} recently, with ${Math.random() > 0.5 ? 'moderate' : 'significant'} price fluctuations.</p>
                                <p><strong>Confidence:</strong> There is a ${widget.options.confidenceInterval * 100}% probability that the actual price will fall within the predicted range.</p>
                            `;
                        }
                    }, 2000);
                });

                // Handle settings form
                const applySettingsButton = document.getElementById('apply-settings');
                const resetSettingsButton = document.getElementById('reset-settings');

                if (applySettingsButton) {
                    applySettingsButton.addEventListener('click', function() {
                        // Show loading state
                        document.querySelectorAll('.grid-container > div').forEach(container => {
                            if (container.id && container.id.includes('prediction')) {
                                container.innerHTML = `
                                    <div class="loading-container">
                                        <div class="loading-spinner"></div>
                                        <span class="loading-text">Updating predictions...</span>
                                    </div>
                                `;
                            }
                        });

                        // Get form values
                        const modelType = document.getElementById('model-type').value;
                        const timeHorizon = parseInt(document.getElementById('time-horizon').value, 10);
                        const confidenceInterval = parseFloat(document.getElementById('confidence-interval').value);
                        const includeSentiment = document.getElementById('include-sentiment').value === 'true';
                        const dataSources = document.getElementById('data-sources').value;
                        const volatilityAdjustment = document.getElementById('volatility-adjustment').value;

                        // Update widgets with new settings
                        const settings = {
                            days: timeHorizon,
                            modelType: modelType,
                            confidenceInterval: confidenceInterval,
                            includeSentiment: includeSentiment,
                            dataSources: dataSources,
                            volatilityAdjustment: volatilityAdjustment
                        };

                        // Simulate processing delay
                        setTimeout(() => {
                            // Apply settings and reload data for all widgets
                            widgets.forEach(widget => {
                                widget.options = { ...widget.options, ...settings };
                                widget.render();
                                widget.loadData();
                            });

                            // Show success notification
                            const notification = document.createElement('div');
                            notification.style.position = 'fixed';
                            notification.style.bottom = '20px';
                            notification.style.right = '20px';
                            notification.style.backgroundColor = '#4caf50';
                            notification.style.color = 'white';
                            notification.style.padding = '10px 20px';
                            notification.style.borderRadius = '4px';
                            notification.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
                            notification.style.zIndex = '1000';
                            notification.style.display = 'flex';
                            notification.style.alignItems = 'center';
                            notification.innerHTML = '<i class="fas fa-check-circle" style="margin-right: 10px;"></i> Settings applied successfully!';

                            document.body.appendChild(notification);

                            // Remove notification after 3 seconds
                            setTimeout(() => {
                                notification.style.opacity = '0';
                                notification.style.transition = 'opacity 0.5s';
                                setTimeout(() => notification.remove(), 500);
                            }, 3000);
                        }, 1500);
                    });
                }

                if (resetSettingsButton) {
                    resetSettingsButton.addEventListener('click', function() {
                        // Reset form values
                        document.getElementById('model-type').value = 'random_forest';
                        document.getElementById('time-horizon').value = '7';
                        document.getElementById('confidence-interval').value = '0.9';
                        document.getElementById('include-sentiment').value = 'true';
                        document.getElementById('data-sources').value = 'all';
                        document.getElementById('volatility-adjustment').value = 'auto';

                        // Show notification
                        const notification = document.createElement('div');
                        notification.style.position = 'fixed';
                        notification.style.bottom = '20px';
                        notification.style.right = '20px';
                        notification.style.backgroundColor = '#1976d2';
                        notification.style.color = 'white';
                        notification.style.padding = '10px 20px';
                        notification.style.borderRadius = '4px';
                        notification.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
                        notification.style.zIndex = '1000';
                        notification.style.display = 'flex';
                        notification.style.alignItems = 'center';
                        notification.innerHTML = '<i class="fas fa-undo" style="margin-right: 10px;"></i> Settings reset to defaults';

                        document.body.appendChild(notification);

                        // Remove notification after 3 seconds
                        setTimeout(() => {
                            notification.style.opacity = '0';
                            notification.style.transition = 'opacity 0.5s';
                            setTimeout(() => notification.remove(), 500);
                        }, 3000);
                    });
                }
            }
        });
    </script>
</body>
</html>
