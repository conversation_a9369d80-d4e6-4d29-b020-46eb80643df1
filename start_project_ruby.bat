@echo off
color 0A
echo.
echo ========================================
echo    🚀 PROJECT RUBY TURBO STARTUP 🚀
echo         High-Performance Mode
echo ========================================
echo.

REM Install required packages if not present
echo 📦 Checking dependencies...
python -m pip install --quiet fastapi uvicorn aiohttp requests numpy > nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Installing missing dependencies...
    python -m pip install fastapi uvicorn aiohttp requests numpy
)

REM Start all background processes in parallel for maximum speed
echo 🔄 Starting background processes in parallel...

REM Start signal generation in background (non-blocking)
start "Signal Generator" /min cmd /c "python generate_timeframe_signals.py > nul 2>&1"

REM Start consensus models initialization in background (non-blocking)
start "Consensus Init" /min cmd /c "python -c \"try: from backend.models.consensus_engine import ConsensusEngine; engine = ConsensusEngine(); engine.generate_predictions_for_symbols(['BTC', 'ETH', 'SOL', 'ADA'], force_refresh=True); print('AI models ready'); except: print('AI models skipped')\" > nul 2>&1"

REM Check and start price server with optimizations
netstat -ano | findstr :8004 > nul
if %errorlevel% equ 0 (
    echo ✅ Price server already running on port 8004
) else (
    echo 🚀 Starting optimized price server...
    start "High-Performance Price Server" cmd /c "cd backend && python price_server.py"
    echo ⏳ Waiting for price server to initialize...
    timeout /t 2 > nul
)

REM Check and start frontend server
netstat -ano | findstr :8085 > nul
if %errorlevel% equ 0 (
    echo ✅ Frontend server already running on port 8085
) else (
    echo 🌐 Starting frontend server...
    start "Frontend Server" cmd /c "cd frontend\public && python -m http.server 8085"
    timeout /t 1 > nul
)

REM Start signal updater with reduced resource usage
echo 📊 Starting signal updater...
start "Signal Updater" /min cmd /c "python backend\signal_updater.py"

REM Quick health check
echo 🔍 Performing health check...
timeout /t 1 > nul
curl -s http://localhost:8004/health > nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ API server is healthy
) else (
    echo ⚠️  API server may still be starting...
)

REM Open application with turbo mode
echo 🌟 Opening Project Ruby with Turbo Mode...
start http://localhost:8085/dashboard.html

echo.
echo ========================================
echo ✅ PROJECT RUBY TURBO STARTUP COMPLETE
echo ========================================
echo 🚀 Frontend: http://localhost:8085
echo ⚡ API Server: http://localhost:8004
echo 📊 Performance: http://localhost:8004/api/performance
echo.
echo 🎯 Features Enabled:
echo   • High-performance caching
echo   • Async HTTP connections
echo   • Service worker caching
echo   • Parallel data loading
echo   • Smart request batching
echo.
echo 💡 Tip: Check browser console for performance metrics!
echo.
echo Press any key to exit (servers will continue running)...
pause > nul
