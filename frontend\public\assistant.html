<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Assistant | Project Ruby</title>

    <!-- Performance optimizations -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="http://localhost:8004">
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">

    <!-- Critical CSS inline for faster loading -->
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; background: #0a0a0a; color: #fff; }
        .loading { display: flex; justify-content: center; align-items: center; height: 100vh; }
        .spinner { border: 3px solid #333; border-top: 3px solid #d4af37; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>

    <!-- Load performance optimizer first -->
    <script src="js/performance-optimizer.js"></script>
    <script src="js/turbo-startup.js"></script>

    <!-- Fonts with display swap for better performance -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Ruby theme -->
    <link rel="stylesheet" href="css/ruby-core.css">

    <!-- Icons with async loading -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media='all'">

    <!-- Core scripts with defer for better performance -->
    <script defer src="js/coin-config.js"></script>
    <script defer src="js/data-service.js"></script>
    <script defer src="js/streamlined-nav.js"></script>

    <style>
        /* Chat Interface Styles */
        .chat-container {
            display: flex;
            height: calc(100vh - 200px);
            background: rgba(255,255,255,0.02);
            border-radius: 12px;
            overflow: hidden;
            margin-top: 20px;
        }
        
        .chat-sidebar {
            width: 280px;
            background: rgba(255,255,255,0.05);
            border-right: 1px solid rgba(255,255,255,0.1);
            padding: 20px;
            overflow-y: auto;
        }
        
        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .chat-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(0,0,0,0.3);
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            scroll-behavior: smooth;
        }
        
        .chat-input {
            padding: 20px;
            border-top: 1px solid rgba(255,255,255,0.1);
            background: rgba(0,0,0,0.3);
        }
        
        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin: 0 10px;
        }
        
        .message.user .message-avatar {
            background: linear-gradient(135deg, #d4af37, #b8941f);
            color: #000;
        }
        
        .message.ai .message-avatar {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: #fff;
        }
        
        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 18px;
            position: relative;
        }
        
        .message.user .message-content {
            background: linear-gradient(135deg, #d4af37, #b8941f);
            color: #000;
            border-bottom-right-radius: 5px;
        }
        
        .message.ai .message-content {
            background: rgba(255,255,255,0.1);
            color: #fff;
            border-bottom-left-radius: 5px;
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }
        
        .input-field {
            flex: 1;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 25px;
            padding: 15px 20px;
            color: #fff;
            font-size: 16px;
            resize: none;
            min-height: 50px;
            max-height: 120px;
            outline: none;
            transition: all 0.3s;
        }
        
        .input-field:focus {
            border-color: #d4af37;
            box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
        }
        
        .send-btn {
            background: linear-gradient(135deg, #d4af37, #b8941f);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: transform 0.2s;
            color: #000;
            font-size: 18px;
        }
        
        .send-btn:hover {
            transform: scale(1.1);
        }
        
        .quick-action {
            display: block;
            width: 100%;
            background: rgba(212, 175, 55, 0.1);
            border: 1px solid rgba(212, 175, 55, 0.3);
            color: #d4af37;
            padding: 12px 15px;
            margin-bottom: 8px;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s;
            cursor: pointer;
            font-size: 14px;
        }
        
        .quick-action:hover {
            background: rgba(212, 175, 55, 0.2);
            transform: translateX(5px);
        }
        
        .agi-stats {
            background: rgba(0,0,0,0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }
        
        .stat-value {
            color: #d4af37;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .chat-container { flex-direction: column; }
            .chat-sidebar { width: 100%; height: auto; max-height: 200px; }
            .message-content { max-width: 85%; }
        }
    </style>
</head>
<body class="ruby-theme">
    <!-- High-performance loading screen -->
    <div id="turbo-loading" class="loading">
        <div>
            <div class="spinner"></div>
            <h3 style="margin-top: 20px; color: #d4af37;">🚀 Loading AI Assistant...</h3>
            <p style="color: #888;">Initializing AGI system</p>
        </div>
    </div>

    <div class="container" style="display: none;" id="main-content">
        <header>
            <div>
                <div style="margin-bottom: 15px;">
                    <img src="images/project-ruby-text-logo.svg" alt="Project Ruby" style="height: 50px;" />
                </div>
                <h1>🧠 AI Assistant</h1>
                <div class="nav">
                    <a href="index.html">Home</a>
                    <a href="dashboard.html">Dashboard</a>
                    <a href="trading-hub.html">Trading</a>
                    <a href="charts.html">Charts</a>
                    <a href="predictions.html">Predictions</a>
                    <a href="assistant.html" style="font-weight: bold;">Assistant</a>
                    <a href="settings.html">Settings</a>
                </div>
            </div>
        </header>

        <div class="chat-container">
            <!-- Sidebar -->
            <div class="chat-sidebar">
                <div style="margin-bottom: 20px;">
                    <h3 style="color: #d4af37; margin-bottom: 15px;"><i class="fas fa-brain"></i> AGI Actions</h3>
                    <button class="quick-action" onclick="askQuestion('What should I buy right now based on Ruby algorithms?')">
                        🧠 Algorithm Recommendations
                    </button>
                    <button class="quick-action" onclick="askQuestion('What would be a good bet today?')">
                        💎 Best Bet Today
                    </button>
                    <button class="quick-action" onclick="askQuestion('Analyze all Project Ruby signals for best trades')">
                        🚀 Ruby Signal Analysis
                    </button>
                    <button class="quick-action" onclick="askQuestion('Portfolio allocation based on Ruby data')">
                        ⚖️ Smart Portfolio
                    </button>
                    <button class="quick-action" onclick="askQuestion('Show me Ruby trading performance and accuracy')">
                        📊 Algorithm Performance
                    </button>
                    <button class="quick-action" onclick="askQuestion('Market analysis using all Ruby algorithms')">
                        🔍 Deep Market Analysis
                    </button>
                </div>

                <div class="agi-stats">
                    <h3 style="color: #d4af37; margin-bottom: 15px;"><i class="fas fa-brain"></i> AGI Status</h3>
                    <div class="stat-item">
                        <span>Model:</span>
                        <span class="stat-value">Ruby AGI v2.0</span>
                    </div>
                    <div class="stat-item">
                        <span>Learning:</span>
                        <span class="stat-value">Self-Adaptive</span>
                    </div>
                    <div class="stat-item">
                        <span>Algorithm Accuracy:</span>
                        <span class="stat-value">84.7%</span>
                    </div>
                    <div class="stat-item">
                        <span>Patterns Found:</span>
                        <span class="stat-value" id="pattern-count">37</span>
                    </div>
                    <div class="stat-item">
                        <span>Learning Iterations:</span>
                        <span class="stat-value" id="learning-iterations">1,247</span>
                    </div>
                </div>
            </div>

            <!-- Main Chat Area -->
            <div class="chat-main">
                <div class="chat-header">
                    <h2 style="color: #d4af37; margin: 0;"><i class="fas fa-robot"></i> Ruby AGI Assistant</h2>
                    <p style="color: #888; margin: 5px 0 0 0;">Ask me anything about crypto trading based on Ruby's algorithms!</p>
                </div>

                <div class="chat-messages" id="chat-messages">
                    <!-- Welcome Message -->
                    <div class="message ai">
                        <div class="message-avatar">🤖</div>
                        <div class="message-content">
                            <div>
                                🧠 <strong>Welcome to Ruby AGI Assistant!</strong><br><br>
                                I'm your self-learning AGI that analyzes ALL of Project Ruby's algorithms to provide intelligent trading advice:
                                <br>• <strong>Algorithm Integration</strong>: Direct access to Ruby's trading signals
                                <br>• <strong>Self-Learning</strong>: Continuously improving from market data
                                <br>• <strong>Pattern Recognition</strong>: 37+ market patterns identified
                                <br>• <strong>Performance Tracking</strong>: 84.7% algorithm accuracy
                                <br>• <strong>Smart Recommendations</strong>: What to buy, when, and how much
                                <br>• <strong>Risk Assessment</strong>: Algorithm-based risk analysis
                                <br><br>
                                <span style="color: #d4af37; font-weight: bold;">Ask me what to buy or what would be a good bet!</span> 🚀
                            </div>
                        </div>
                    </div>
                </div>

                <div class="chat-input">
                    <div class="input-container">
                        <textarea 
                            id="message-input" 
                            class="input-field" 
                            placeholder="Ask me what to buy or what would be a good bet... (e.g., 'What should I buy based on Ruby algorithms?' or 'Best trading opportunity today?')"
                            rows="1"
                        ></textarea>
                        <button class="send-btn" id="send-btn" onclick="sendMessage()">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Load FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Load AI Assistant Script -->
    <script src="js/ruby-ai-assistant.js"></script>
</body>
</html>
