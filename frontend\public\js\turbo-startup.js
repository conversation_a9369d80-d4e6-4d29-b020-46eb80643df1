/**
 * Turbo Startup Module for Project Ruby
 * Parallel loading and initialization for maximum speed
 */

class TurboStartup {
    constructor() {
        this.startTime = performance.now();
        this.loadedModules = new Set();
        this.initPromises = new Map();
        this.criticalData = new Map();
        
        this.init();
    }

    async init() {
        console.log('🚀 Turbo Startup initiated...');
        
        // Start all critical operations in parallel
        const parallelTasks = [
            this.preloadCriticalData(),
            this.initializeServices(),
            this.setupEventListeners(),
            this.optimizeDOM(),
            this.prefetchResources()
        ];

        try {
            await Promise.all(parallelTasks);
            this.onStartupComplete();
        } catch (error) {
            console.error('Startup error:', error);
            this.handleStartupError(error);
        }
    }

    async preloadCriticalData() {
        console.log('📊 Preloading critical data...');
        
        const criticalEndpoints = [
            { key: 'btc_price', url: 'http://localhost:8004/api/cc/price/BTC' },
            { key: 'eth_price', url: 'http://localhost:8004/api/cc/price/ETH' },
            { key: 'sol_price', url: 'http://localhost:8004/api/cc/price/SOL' },
            { key: 'signals', url: 'http://localhost:8004/signals' },
            { key: 'health', url: 'http://localhost:8004/health' }
        ];

        const fetchPromises = criticalEndpoints.map(async ({ key, url }) => {
            try {
                const response = await fetch(url, {
                    headers: { 'Accept': 'application/json' },
                    signal: AbortSignal.timeout(5000) // 5 second timeout
                });
                
                if (response.ok) {
                    const data = await response.json();
                    this.criticalData.set(key, data);
                    console.log(`✅ Loaded ${key}`);
                } else {
                    console.warn(`⚠️ Failed to load ${key}: ${response.status}`);
                }
            } catch (error) {
                console.warn(`⚠️ Error loading ${key}:`, error.message);
                // Set fallback data
                this.setFallbackData(key);
            }
        });

        await Promise.allSettled(fetchPromises);
    }

    setFallbackData(key) {
        const fallbacks = {
            btc_price: { symbol: 'BTC', price: 93747.66, source: 'fallback' },
            eth_price: { symbol: 'ETH', price: 1781.41, source: 'fallback' },
            sol_price: { symbol: 'SOL', price: 153.92, source: 'fallback' },
            signals: [],
            health: { status: 'offline', timestamp: new Date().toISOString() }
        };
        
        if (fallbacks[key]) {
            this.criticalData.set(key, fallbacks[key]);
        }
    }

    async initializeServices() {
        console.log('⚙️ Initializing services...');
        
        const serviceInits = [
            this.initDataService(),
            this.initMarketDataAPI(),
            this.initSignalAnalysis(),
            this.initCoinConfig(),
            this.initPerformanceOptimizer()
        ];

        await Promise.allSettled(serviceInits);
    }

    async initDataService() {
        if (typeof DataService !== 'undefined') {
            try {
                await DataService.authenticate();
                this.loadedModules.add('DataService');
                console.log('✅ DataService initialized');
            } catch (error) {
                console.warn('⚠️ DataService init failed:', error);
            }
        }
    }

    async initMarketDataAPI() {
        if (typeof MarketDataAPI !== 'undefined') {
            this.loadedModules.add('MarketDataAPI');
            console.log('✅ MarketDataAPI initialized');
        }
    }

    async initSignalAnalysis() {
        if (typeof SignalAnalysisService !== 'undefined') {
            this.loadedModules.add('SignalAnalysisService');
            console.log('✅ SignalAnalysisService initialized');
        }
    }

    async initCoinConfig() {
        if (typeof CoinConfigService !== 'undefined') {
            this.loadedModules.add('CoinConfigService');
            console.log('✅ CoinConfigService initialized');
        }
    }

    async initPerformanceOptimizer() {
        if (typeof PerformanceOptimizer !== 'undefined') {
            this.loadedModules.add('PerformanceOptimizer');
            console.log('✅ PerformanceOptimizer initialized');
        }
    }

    setupEventListeners() {
        console.log('🎧 Setting up event listeners...');
        
        // Optimized scroll handling
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            if (scrollTimeout) return;
            scrollTimeout = setTimeout(() => {
                this.handleScroll();
                scrollTimeout = null;
            }, 16); // ~60fps
        }, { passive: true });

        // Optimized resize handling
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.handleResize();
            }, 250);
        });

        // Visibility change for performance optimization
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseUpdates();
            } else {
                this.resumeUpdates();
            }
        });

        // Connection status monitoring
        window.addEventListener('online', () => {
            console.log('🌐 Connection restored');
            this.handleConnectionRestore();
        });

        window.addEventListener('offline', () => {
            console.log('📴 Connection lost');
            this.handleConnectionLoss();
        });
    }

    optimizeDOM() {
        console.log('🎨 Optimizing DOM...');
        
        // Remove unused elements
        const unusedElements = document.querySelectorAll('.unused, .hidden-mobile');
        unusedElements.forEach(el => {
            if (window.innerWidth < 768 && el.classList.contains('hidden-mobile')) {
                el.remove();
            }
        });

        // Optimize images
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (!img.loading) {
                img.loading = 'lazy';
            }
            if (!img.decoding) {
                img.decoding = 'async';
            }
        });

        // Add performance hints
        const head = document.head;
        const preconnect = document.createElement('link');
        preconnect.rel = 'preconnect';
        preconnect.href = 'http://localhost:8004';
        head.appendChild(preconnect);
    }

    async prefetchResources() {
        console.log('🔄 Prefetching resources...');
        
        const prefetchUrls = [
            '/trading.html',
            '/charts.html',
            '/predictions.html',
            '/api/cc/indicators/BTC',
            '/api/cc/indicators/ETH'
        ];

        prefetchUrls.forEach(url => {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = url.startsWith('/api') ? `http://localhost:8004${url}` : url;
            document.head.appendChild(link);
        });
    }

    handleScroll() {
        // Implement virtual scrolling for large lists
        const tables = document.querySelectorAll('table tbody');
        tables.forEach(tbody => {
            const rows = tbody.querySelectorAll('tr');
            if (rows.length > 50) {
                this.implementVirtualScrolling(tbody);
            }
        });
    }

    handleResize() {
        // Responsive optimizations
        if (window.innerWidth < 768) {
            this.enableMobileOptimizations();
        } else {
            this.enableDesktopOptimizations();
        }
    }

    pauseUpdates() {
        // Pause non-critical updates when tab is hidden
        if (window.updateIntervals) {
            window.updateIntervals.forEach(interval => clearInterval(interval));
        }
    }

    resumeUpdates() {
        // Resume updates when tab becomes visible
        this.triggerDataRefresh();
    }

    handleConnectionRestore() {
        // Sync data when connection is restored
        this.triggerDataRefresh();
    }

    handleConnectionLoss() {
        // Switch to offline mode
        this.enableOfflineMode();
    }

    enableMobileOptimizations() {
        document.body.classList.add('mobile-optimized');
        // Reduce update frequency on mobile
        if (window.updateInterval) {
            clearInterval(window.updateInterval);
            window.updateInterval = setInterval(this.updateData, 10000); // 10 seconds
        }
    }

    enableDesktopOptimizations() {
        document.body.classList.remove('mobile-optimized');
        // Increase update frequency on desktop
        if (window.updateInterval) {
            clearInterval(window.updateInterval);
            window.updateInterval = setInterval(this.updateData, 5000); // 5 seconds
        }
    }

    enableOfflineMode() {
        document.body.classList.add('offline-mode');
        const offlineIndicator = document.createElement('div');
        offlineIndicator.className = 'offline-indicator';
        offlineIndicator.textContent = '📴 Offline Mode';
        document.body.appendChild(offlineIndicator);
    }

    triggerDataRefresh() {
        // Trigger a fresh data load
        window.dispatchEvent(new CustomEvent('data-refresh-requested'));
    }

    onStartupComplete() {
        const loadTime = performance.now() - this.startTime;
        console.log(`🎉 Turbo Startup complete in ${loadTime.toFixed(2)}ms`);
        console.log(`📊 Loaded modules: ${Array.from(this.loadedModules).join(', ')}`);
        console.log(`💾 Critical data loaded: ${this.criticalData.size} items`);
        
        // Dispatch startup complete event
        window.dispatchEvent(new CustomEvent('turbo-startup-complete', {
            detail: {
                loadTime,
                loadedModules: Array.from(this.loadedModules),
                criticalData: Object.fromEntries(this.criticalData)
            }
        }));

        // Show performance metrics
        this.showPerformanceMetrics(loadTime);
    }

    showPerformanceMetrics(loadTime) {
        if (loadTime < 1000) {
            console.log('🚀 BLAZING FAST! Load time under 1 second');
        } else if (loadTime < 2000) {
            console.log('⚡ FAST! Load time under 2 seconds');
        } else {
            console.log('🐌 Could be faster. Load time:', loadTime.toFixed(2) + 'ms');
        }
    }

    handleStartupError(error) {
        console.error('❌ Startup failed:', error);
        
        // Fallback to basic functionality
        document.body.classList.add('startup-error');
        
        // Show error message to user
        const errorDiv = document.createElement('div');
        errorDiv.className = 'startup-error-message';
        errorDiv.innerHTML = `
            <h3>⚠️ Startup Error</h3>
            <p>Some features may not work properly. Please refresh the page.</p>
            <button onclick="location.reload()">Refresh Page</button>
        `;
        document.body.appendChild(errorDiv);
    }

    // Public API
    getCriticalData(key) {
        return this.criticalData.get(key);
    }

    isModuleLoaded(moduleName) {
        return this.loadedModules.has(moduleName);
    }
}

// Initialize Turbo Startup
const turboStartup = new TurboStartup();

// Export for global use
window.TurboStartup = turboStartup;
