<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Ruby - Redirecting...</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #121212;
            color: #e0e0e0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
            text-align: center;
        }
        h1 {
            color: #d4af37;
            margin-bottom: 20px;
        }
        p {
            margin-bottom: 30px;
            max-width: 600px;
            line-height: 1.6;
        }
        .loader {
            border: 5px solid #333;
            border-top: 5px solid #d4af37;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin-bottom: 30px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .links {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
        }
        .link-button {
            background-color: #1e1e1e;
            color: #e0e0e0;
            border: 1px solid #333;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .link-button:hover {
            background-color: #2a2a2a;
            border-color: #d4af37;
        }
    </style>
</head>
<body>
    <h1>Project Ruby</h1>
    <div class="loader"></div>
    <p>Redirecting you to the correct page...</p>
    <p>If you are not redirected automatically, please click one of the links below:</p>

    <div class="links">
        <a href="trading-hub.html" class="link-button">🚀 Trading Hub</a>
        <a href="dashboard.html" class="link-button">Dashboard</a>
        <a href="charts.html" class="link-button">Charts</a>
        <a href="trading.html" class="link-button">Trading Signals</a>
        <a href="settings.html" class="link-button">Settings</a>
    </div>

    <script>
        // Check which pages exist and redirect to the first available one
        const pages = [
            'trading-hub.html',
            'assistant.html',
            'dashboard.html',
            'charts.html',
            'index.html'
        ];

        function checkPage(index) {
            if (index >= pages.length) {
                // If no pages are found, stay on this page
                document.querySelector('.loader').style.display = 'none';
                document.querySelector('p').textContent = 'Could not find any available pages. Please click one of the links below.';
                return;
            }

            const page = pages[index];
            fetch(page, { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        window.location.href = page;
                    } else {
                        checkPage(index + 1);
                    }
                })
                .catch(() => {
                    checkPage(index + 1);
                });
        }

        // Start checking pages after a short delay
        setTimeout(() => {
            checkPage(0);
        }, 1000);
    </script>
</body>
</html>
