<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Hub | Project Ruby - Your Trading Command Center</title>
    
    <!-- Performance optimizations -->
    <link rel="preconnect" href="http://localhost:8004">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    
    <!-- Critical CSS -->
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #fff; 
            line-height: 1.6;
        }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        
        /* Header */
        .header { 
            background: rgba(0,0,0,0.8); 
            padding: 15px 0; 
            border-bottom: 2px solid #d4af37;
            margin-bottom: 30px;
        }
        .header-content { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            max-width: 1400px; 
            margin: 0 auto; 
            padding: 0 20px;
        }
        .logo { 
            font-size: 24px; 
            font-weight: bold; 
            color: #d4af37; 
            display: flex; 
            align-items: center;
        }
        .logo i { margin-right: 10px; font-size: 28px; }
        .nav { display: flex; gap: 30px; }
        .nav a { 
            color: #fff; 
            text-decoration: none; 
            font-weight: 500; 
            transition: color 0.3s;
            padding: 8px 16px;
            border-radius: 4px;
        }
        .nav a:hover, .nav a.active { 
            color: #d4af37; 
            background: rgba(212, 175, 55, 0.1);
        }
        
        /* Main Grid */
        .main-grid { 
            display: grid; 
            grid-template-columns: 1fr 350px; 
            gap: 30px; 
            margin-bottom: 30px;
        }
        
        /* Cards */
        .card { 
            background: rgba(255,255,255,0.05); 
            border-radius: 12px; 
            padding: 25px; 
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
        }
        .card h2 { 
            color: #d4af37; 
            margin-bottom: 20px; 
            display: flex; 
            align-items: center;
            font-size: 20px;
        }
        .card h2 i { margin-right: 10px; }
        
        /* Trading Signals */
        .signal-item { 
            background: rgba(0,0,0,0.3); 
            border-radius: 8px; 
            padding: 20px; 
            margin-bottom: 15px;
            border-left: 4px solid transparent;
            transition: transform 0.2s;
        }
        .signal-item:hover { transform: translateY(-2px); }
        .signal-item.buy { border-left-color: #4CAF50; }
        .signal-item.sell { border-left-color: #f44336; }
        .signal-item.hold { border-left-color: #ff9800; }
        
        .signal-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 15px;
        }
        .signal-coin { 
            font-size: 18px; 
            font-weight: bold; 
            display: flex; 
            align-items: center;
        }
        .signal-coin i { margin-right: 8px; color: #d4af37; }
        
        .signal-badge { 
            padding: 6px 12px; 
            border-radius: 20px; 
            font-size: 12px; 
            font-weight: bold; 
            text-transform: uppercase;
        }
        .signal-badge.buy { background: #4CAF50; color: white; }
        .signal-badge.sell { background: #f44336; color: white; }
        .signal-badge.hold { background: #ff9800; color: white; }
        
        .signal-details { 
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 15px; 
            margin-bottom: 15px;
        }
        .detail-item { text-align: center; }
        .detail-label { 
            font-size: 12px; 
            color: #888; 
            margin-bottom: 5px;
        }
        .detail-value { 
            font-size: 16px; 
            font-weight: bold; 
            color: #d4af37;
        }
        
        .trading-instruction { 
            background: rgba(212, 175, 55, 0.1); 
            border-radius: 6px; 
            padding: 15px; 
            border-left: 3px solid #d4af37;
        }
        .instruction-title { 
            font-weight: bold; 
            color: #d4af37; 
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        .instruction-title i { margin-right: 8px; }
        
        /* Sidebar */
        .sidebar .card { margin-bottom: 20px; }
        .metric-item { 
            display: flex; 
            justify-content: space-between; 
            padding: 10px 0; 
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .metric-item:last-child { border-bottom: none; }
        .metric-label { color: #888; }
        .metric-value { font-weight: bold; color: #d4af37; }
        
        /* Coin Rankings */
        .coin-rank { 
            display: flex; 
            align-items: center; 
            padding: 12px 0; 
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .coin-rank:last-child { border-bottom: none; }
        .rank-number { 
            background: #d4af37; 
            color: #000; 
            width: 24px; 
            height: 24px; 
            border-radius: 50%; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            font-size: 12px; 
            font-weight: bold; 
            margin-right: 12px;
        }
        .coin-info { flex: 1; }
        .coin-symbol { font-weight: bold; }
        .coin-score { 
            background: rgba(76, 175, 80, 0.2); 
            color: #4CAF50; 
            padding: 2px 8px; 
            border-radius: 12px; 
            font-size: 11px; 
            font-weight: bold;
        }
        
        /* Action Buttons */
        .action-btn { 
            background: linear-gradient(135deg, #d4af37, #b8941f); 
            color: #000; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 6px; 
            font-weight: bold; 
            cursor: pointer; 
            transition: transform 0.2s;
            margin-top: 15px;
            width: 100%;
        }
        .action-btn:hover { transform: translateY(-1px); }
        
        /* Status Indicators */
        .status-indicator { 
            display: inline-flex; 
            align-items: center; 
            padding: 4px 8px; 
            border-radius: 12px; 
            font-size: 11px; 
            font-weight: bold;
        }
        .status-active { background: rgba(76, 175, 80, 0.2); color: #4CAF50; }
        .status-warning { background: rgba(255, 152, 0, 0.2); color: #ff9800; }
        
        /* Responsive */
        @media (max-width: 1024px) {
            .main-grid { grid-template-columns: 1fr; }
            .signal-details { grid-template-columns: 1fr; }
        }
    </style>
    
    <!-- Load performance optimizer -->
    <script src="js/performance-optimizer.js"></script>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-gem"></i>
                Project Ruby Trading Hub
            </div>
            <nav class="nav">
                <a href="#" class="active">Trading Hub</a>
                <a href="charts.html">Charts</a>
                <a href="dashboard.html">Dashboard</a>
                <a href="settings.html">Settings</a>
            </nav>
        </div>
    </div>

    <div class="container">
        <!-- Main Grid -->
        <div class="main-grid">
            <!-- Main Content -->
            <div>
                <!-- Active Trading Signals -->
                <div class="card">
                    <h2><i class="fas fa-bullseye"></i>Active Trading Signals</h2>
                    <div id="trading-signals">
                        <!-- Signals will be loaded here -->
                        <div class="signal-item buy">
                            <div class="signal-header">
                                <div class="signal-coin">
                                    <i class="fab fa-bitcoin"></i>
                                    Bitcoin (BTC)
                                </div>
                                <div class="signal-badge buy">Strong Buy</div>
                            </div>
                            <div class="signal-details">
                                <div class="detail-item">
                                    <div class="detail-label">Current Price</div>
                                    <div class="detail-value">$93,747</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Confidence</div>
                                    <div class="detail-value">87%</div>
                                </div>
                            </div>
                            <div class="trading-instruction">
                                <div class="instruction-title">
                                    <i class="fas fa-lightbulb"></i>
                                    Trading Instruction
                                </div>
                                <strong>Entry:</strong> $93,500 - $94,000<br>
                                <strong>Target:</strong> $98,000 (4.5% gain)<br>
                                <strong>Stop Loss:</strong> $91,000 (2.9% risk)<br>
                                <strong>Position Size:</strong> 2-3% of portfolio
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Market Overview -->
                <div class="card">
                    <h2><i class="fas fa-chart-line"></i>Market Overview</h2>
                    <div class="signal-details">
                        <div class="detail-item">
                            <div class="detail-label">Market Sentiment</div>
                            <div class="detail-value">Bullish</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Fear & Greed Index</div>
                            <div class="detail-value">72 (Greed)</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Active Signals</div>
                            <div class="detail-value">3 Buy, 1 Hold</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Success Rate (7d)</div>
                            <div class="detail-value">84%</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="sidebar">
                <!-- Top Coins to Trade -->
                <div class="card">
                    <h2><i class="fas fa-trophy"></i>Top Coins to Trade</h2>
                    <div id="top-coins">
                        <div class="coin-rank">
                            <div class="rank-number">1</div>
                            <div class="coin-info">
                                <div class="coin-symbol">BTC</div>
                                <div style="font-size: 12px; color: #888;">Strong momentum</div>
                            </div>
                            <div class="coin-score">95</div>
                        </div>
                        <div class="coin-rank">
                            <div class="rank-number">2</div>
                            <div class="coin-info">
                                <div class="coin-symbol">ETH</div>
                                <div style="font-size: 12px; color: #888;">Bullish breakout</div>
                            </div>
                            <div class="coin-score">89</div>
                        </div>
                        <div class="coin-rank">
                            <div class="rank-number">3</div>
                            <div class="coin-info">
                                <div class="coin-symbol">SOL</div>
                                <div style="font-size: 12px; color: #888;">High volume</div>
                            </div>
                            <div class="coin-score">82</div>
                        </div>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <div class="card">
                    <h2><i class="fas fa-chart-bar"></i>Performance</h2>
                    <div class="metric-item">
                        <span class="metric-label">Today's P&L</span>
                        <span class="metric-value">+$2,847</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">Win Rate (30d)</span>
                        <span class="metric-value">78%</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">Avg. Gain</span>
                        <span class="metric-value">4.2%</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">Risk Score</span>
                        <span class="metric-value">Low</span>
                    </div>
                </div>

                <!-- System Status -->
                <div class="card">
                    <h2><i class="fas fa-cog"></i>System Status</h2>
                    <div class="metric-item">
                        <span class="metric-label">Signal Engine</span>
                        <span class="status-indicator status-active">Active</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">Data Feed</span>
                        <span class="status-indicator status-active">Live</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">Last Update</span>
                        <span class="metric-value">2 min ago</span>
                    </div>
                    <button class="action-btn">
                        <i class="fas fa-sync"></i> Refresh Signals
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Load FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Load core scripts -->
    <script src="js/trading-hub.js"></script>
</body>
</html>
