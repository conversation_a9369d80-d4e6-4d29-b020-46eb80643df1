/**
 * Trading Hub - Core Trading Instruction Engine
 * Provides clear, actionable trading guidance
 */

class TradingHub {
    constructor() {
        this.apiBase = 'http://localhost:8004';
        this.signals = [];
        this.topCoins = [];
        this.marketData = {};
        this.updateInterval = null;
        
        this.init();
    }

    async init() {
        console.log('🚀 Trading Hub initializing...');
        
        // Load initial data
        await this.loadTradingSignals();
        await this.loadTopCoins();
        await this.loadMarketOverview();
        
        // Start real-time updates
        this.startRealTimeUpdates();
        
        // Setup event listeners
        this.setupEventListeners();
        
        console.log('✅ Trading Hub ready!');
    }

    async loadTradingSignals() {
        try {
            const response = await fetch(`${this.apiBase}/signals`);
            const data = await response.json();
            
            this.signals = this.processSignals(data);
            this.renderTradingSignals();
            
        } catch (error) {
            console.error('Error loading signals:', error);
            this.showFallbackSignals();
        }
    }

    processSignals(rawSignals) {
        return rawSignals.map(signal => {
            const processed = {
                ...signal,
                instruction: this.generateTradingInstruction(signal),
                riskLevel: this.calculateRiskLevel(signal),
                positionSize: this.calculatePositionSize(signal)
            };
            
            return processed;
        });
    }

    generateTradingInstruction(signal) {
        const price = signal.price || 0;
        const confidence = signal.confidence || 0.5;
        const signalType = signal.signal_type || signal.signal || 'hold';
        
        let instruction = {};
        
        if (signalType === 'buy') {
            const entryLow = price * 0.995;
            const entryHigh = price * 1.005;
            const target = price * (1 + (0.03 + confidence * 0.05)); // 3-8% target
            const stopLoss = price * (1 - (0.02 + (1 - confidence) * 0.03)); // 2-5% stop
            
            instruction = {
                action: 'BUY',
                entry: `$${entryLow.toFixed(0)} - $${entryHigh.toFixed(0)}`,
                target: `$${target.toFixed(0)} (${((target/price - 1) * 100).toFixed(1)}% gain)`,
                stopLoss: `$${stopLoss.toFixed(0)} (${((1 - stopLoss/price) * 100).toFixed(1)}% risk)`,
                positionSize: this.calculatePositionSize(signal),
                timeframe: '1-7 days',
                reasoning: this.generateReasoning(signal, 'buy')
            };
            
        } else if (signalType === 'sell') {
            const entryLow = price * 0.995;
            const entryHigh = price * 1.005;
            const target = price * (1 - (0.03 + confidence * 0.05)); // 3-8% target
            const stopLoss = price * (1 + (0.02 + (1 - confidence) * 0.03)); // 2-5% stop
            
            instruction = {
                action: 'SELL',
                entry: `$${entryLow.toFixed(0)} - $${entryHigh.toFixed(0)}`,
                target: `$${target.toFixed(0)} (${((1 - target/price) * 100).toFixed(1)}% gain)`,
                stopLoss: `$${stopLoss.toFixed(0)} (${((stopLoss/price - 1) * 100).toFixed(1)}% risk)`,
                positionSize: this.calculatePositionSize(signal),
                timeframe: '1-7 days',
                reasoning: this.generateReasoning(signal, 'sell')
            };
            
        } else {
            instruction = {
                action: 'HOLD',
                message: 'No clear trading opportunity. Wait for better setup.',
                reasoning: 'Mixed signals or low confidence. Preserve capital.'
            };
        }
        
        return instruction;
    }

    calculatePositionSize(signal) {
        const confidence = signal.confidence || 0.5;
        const riskLevel = this.calculateRiskLevel(signal);
        
        let baseSize = 2; // Base 2% position
        
        // Adjust based on confidence
        if (confidence > 0.8) baseSize = 4;
        else if (confidence > 0.7) baseSize = 3;
        else if (confidence < 0.6) baseSize = 1;
        
        // Adjust based on risk
        if (riskLevel === 'High') baseSize *= 0.5;
        else if (riskLevel === 'Low') baseSize *= 1.5;
        
        return `${Math.min(5, Math.max(0.5, baseSize)).toFixed(1)}% of portfolio`;
    }

    calculateRiskLevel(signal) {
        const confidence = signal.confidence || 0.5;
        const volatility = signal.indicators?.volatility?.atr_pct || 2;
        
        if (confidence > 0.8 && volatility < 2) return 'Low';
        if (confidence < 0.6 || volatility > 4) return 'High';
        return 'Medium';
    }

    generateReasoning(signal, action) {
        const indicators = signal.indicators || {};
        const reasons = [];
        
        if (action === 'buy') {
            if (indicators.rsi < 40) reasons.push('RSI oversold');
            if (indicators.sma_5 > indicators.sma_10) reasons.push('Short-term uptrend');
            if (signal.confidence > 0.7) reasons.push('High confidence signal');
        } else if (action === 'sell') {
            if (indicators.rsi > 60) reasons.push('RSI overbought');
            if (indicators.sma_5 < indicators.sma_10) reasons.push('Short-term downtrend');
            if (signal.confidence > 0.7) reasons.push('High confidence signal');
        }
        
        return reasons.length > 0 ? reasons.join(', ') : 'Technical analysis confluence';
    }

    renderTradingSignals() {
        const container = document.getElementById('trading-signals');
        if (!container) return;
        
        if (this.signals.length === 0) {
            container.innerHTML = `
                <div class="signal-item hold">
                    <div class="signal-header">
                        <div class="signal-coin">
                            <i class="fas fa-clock"></i>
                            No Active Signals
                        </div>
                        <div class="signal-badge hold">Waiting</div>
                    </div>
                    <div class="trading-instruction">
                        <div class="instruction-title">
                            <i class="fas fa-info-circle"></i>
                            Market Status
                        </div>
                        No clear trading opportunities detected. Monitor for new signals.
                    </div>
                </div>
            `;
            return;
        }
        
        container.innerHTML = this.signals.map(signal => this.renderSignalCard(signal)).join('');
    }

    renderSignalCard(signal) {
        const signalType = signal.signal_type || signal.signal || 'hold';
        const instruction = signal.instruction;
        const confidence = Math.round((signal.confidence || 0.5) * 100);
        const price = signal.price || 0;
        
        const badgeText = signalType === 'buy' ? 'Strong Buy' : 
                         signalType === 'sell' ? 'Strong Sell' : 'Hold';
        
        const coinIcon = this.getCoinIcon(signal.asset_symbol);
        
        return `
            <div class="signal-item ${signalType}">
                <div class="signal-header">
                    <div class="signal-coin">
                        <i class="${coinIcon}"></i>
                        ${signal.asset_name || signal.asset_symbol} (${signal.asset_symbol})
                    </div>
                    <div class="signal-badge ${signalType}">${badgeText}</div>
                </div>
                <div class="signal-details">
                    <div class="detail-item">
                        <div class="detail-label">Current Price</div>
                        <div class="detail-value">$${price.toLocaleString()}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Confidence</div>
                        <div class="detail-value">${confidence}%</div>
                    </div>
                </div>
                <div class="trading-instruction">
                    <div class="instruction-title">
                        <i class="fas fa-lightbulb"></i>
                        Trading Instruction
                    </div>
                    ${this.renderInstruction(instruction)}
                </div>
            </div>
        `;
    }

    renderInstruction(instruction) {
        if (instruction.action === 'HOLD') {
            return `<strong>Action:</strong> ${instruction.message}<br>
                    <strong>Reason:</strong> ${instruction.reasoning}`;
        }
        
        return `
            <strong>Entry:</strong> ${instruction.entry}<br>
            <strong>Target:</strong> ${instruction.target}<br>
            <strong>Stop Loss:</strong> ${instruction.stopLoss}<br>
            <strong>Position Size:</strong> ${instruction.positionSize}<br>
            <strong>Timeframe:</strong> ${instruction.timeframe}
        `;
    }

    getCoinIcon(symbol) {
        const icons = {
            'BTC': 'fab fa-bitcoin',
            'ETH': 'fab fa-ethereum',
            'SOL': 'fas fa-sun',
            'ADA': 'fas fa-heart',
            'DOT': 'fas fa-circle'
        };
        return icons[symbol] || 'fas fa-coins';
    }

    async loadTopCoins() {
        // Generate top coins based on trading score
        this.topCoins = [
            { symbol: 'BTC', name: 'Bitcoin', score: 95, reason: 'Strong momentum' },
            { symbol: 'ETH', name: 'Ethereum', score: 89, reason: 'Bullish breakout' },
            { symbol: 'SOL', name: 'Solana', score: 82, reason: 'High volume' },
            { symbol: 'ADA', name: 'Cardano', score: 76, reason: 'Technical setup' }
        ];
        
        this.renderTopCoins();
    }

    renderTopCoins() {
        const container = document.getElementById('top-coins');
        if (!container) return;
        
        container.innerHTML = this.topCoins.map((coin, index) => `
            <div class="coin-rank">
                <div class="rank-number">${index + 1}</div>
                <div class="coin-info">
                    <div class="coin-symbol">${coin.symbol}</div>
                    <div style="font-size: 12px; color: #888;">${coin.reason}</div>
                </div>
                <div class="coin-score">${coin.score}</div>
            </div>
        `).join('');
    }

    async loadMarketOverview() {
        // Load market overview data
        // This would typically come from your API
        console.log('📊 Market overview loaded');
    }

    showFallbackSignals() {
        // Show fallback signals when API is unavailable
        this.signals = [
            {
                asset_symbol: 'BTC',
                asset_name: 'Bitcoin',
                signal_type: 'buy',
                confidence: 0.87,
                price: 93747,
                indicators: { rsi: 45, sma_5: 94000, sma_10: 92000 }
            }
        ];
        
        this.signals = this.processSignals(this.signals);
        this.renderTradingSignals();
    }

    startRealTimeUpdates() {
        // Update every 30 seconds
        this.updateInterval = setInterval(() => {
            this.loadTradingSignals();
        }, 30000);
    }

    setupEventListeners() {
        // Refresh button
        document.addEventListener('click', (e) => {
            if (e.target.closest('.action-btn')) {
                this.loadTradingSignals();
                this.loadTopCoins();
            }
        });
    }

    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
    }
}

// Initialize Trading Hub when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.tradingHub = new TradingHub();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.tradingHub) {
        window.tradingHub.destroy();
    }
});
