<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Assistant | Project Ruby</title>

    <!-- Performance optimizations -->
    <link rel="preconnect" href="http://localhost:8004">
    <link rel="preconnect" href="https://fonts.googleapis.com">

    <!-- Critical CSS -->
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #fff;
            height: 100vh;
            overflow: hidden;
        }

        /* Header */
        .header {
            background: rgba(0,0,0,0.9);
            padding: 15px 0;
            border-bottom: 2px solid #d4af37;
            backdrop-filter: blur(10px);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #d4af37;
            display: flex;
            align-items: center;
        }
        .logo i { margin-right: 10px; font-size: 28px; }
        .nav { display: flex; gap: 20px; }
        .nav a {
            color: #fff;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
            padding: 8px 16px;
            border-radius: 4px;
        }
        .nav a:hover { color: #d4af37; background: rgba(212, 175, 55, 0.1); }

        /* Main Chat Interface */
        .chat-container {
            display: flex;
            height: calc(100vh - 80px);
            max-width: 1400px;
            margin: 0 auto;
        }

        /* Sidebar */
        .sidebar {
            width: 300px;
            background: rgba(255,255,255,0.05);
            border-right: 1px solid rgba(255,255,255,0.1);
            padding: 20px;
            overflow-y: auto;
        }
        .sidebar h3 {
            color: #d4af37;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .sidebar h3 i { margin-right: 8px; }

        .quick-actions {
            margin-bottom: 30px;
        }
        .action-btn {
            display: block;
            width: 100%;
            background: rgba(212, 175, 55, 0.1);
            border: 1px solid rgba(212, 175, 55, 0.3);
            color: #d4af37;
            padding: 12px 15px;
            margin-bottom: 8px;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s;
            cursor: pointer;
            font-size: 14px;
        }
        .action-btn:hover {
            background: rgba(212, 175, 55, 0.2);
            transform: translateX(5px);
        }

        .crypto-stats {
            background: rgba(0,0,0,0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }
        .stat-value { color: #d4af37; font-weight: bold; }

        /* Main Chat Area */
        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(255,255,255,0.02);
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(0,0,0,0.3);
        }
        .chat-title {
            font-size: 24px;
            color: #d4af37;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }
        .chat-title i { margin-right: 10px; }
        .chat-subtitle {
            color: #888;
            font-size: 14px;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            scroll-behavior: smooth;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }
        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin: 0 10px;
        }
        .message.user .message-avatar {
            background: linear-gradient(135deg, #d4af37, #b8941f);
            color: #000;
        }
        .message.ai .message-avatar {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: #fff;
        }

        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 18px;
            position: relative;
        }
        .message.user .message-content {
            background: linear-gradient(135deg, #d4af37, #b8941f);
            color: #000;
            border-bottom-right-radius: 5px;
        }
        .message.ai .message-content {
            background: rgba(255,255,255,0.1);
            color: #fff;
            border-bottom-left-radius: 5px;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 5px;
        }

        /* Typing Indicator */
        .typing-indicator {
            display: none;
            padding: 15px 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 18px;
            border-bottom-left-radius: 5px;
            max-width: 70px;
        }
        .typing-dots {
            display: flex;
            gap: 4px;
        }
        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #d4af37;
            animation: typing 1.4s infinite;
        }
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); opacity: 0.3; }
            30% { transform: translateY(-10px); opacity: 1; }
        }

        /* Input Area */
        .chat-input {
            padding: 20px;
            border-top: 1px solid rgba(255,255,255,0.1);
            background: rgba(0,0,0,0.3);
        }
        .input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }
        .input-field {
            flex: 1;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 25px;
            padding: 15px 20px;
            color: #fff;
            font-size: 16px;
            resize: none;
            min-height: 50px;
            max-height: 120px;
            outline: none;
            transition: all 0.3s;
        }
        .input-field:focus {
            border-color: #d4af37;
            box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
        }
        .input-field::placeholder {
            color: #888;
        }

        .send-btn {
            background: linear-gradient(135deg, #d4af37, #b8941f);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: transform 0.2s;
            color: #000;
            font-size: 18px;
        }
        .send-btn:hover {
            transform: scale(1.1);
        }
        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* Suggestions */
        .suggestions {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        .suggestion {
            background: rgba(212, 175, 55, 0.1);
            border: 1px solid rgba(212, 175, 55, 0.3);
            color: #d4af37;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
        }
        .suggestion:hover {
            background: rgba(212, 175, 55, 0.2);
            transform: translateY(-2px);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .chat-container { flex-direction: column; }
            .sidebar { width: 100%; height: auto; max-height: 200px; }
            .message-content { max-width: 85%; }
        }

        /* Special Effects */
        .ai-thinking {
            background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
            background-size: 200% 200%;
            animation: gradient 2s ease infinite;
        }

        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .crypto-highlight {
            background: linear-gradient(135deg, #d4af37, #f4d03f);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: bold;
        }

        .price-up { color: #4CAF50; }
        .price-down { color: #f44336; }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(212, 175, 55, 0.3);
            border-radius: 50%;
            border-top-color: #d4af37;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>

    <!-- Load performance optimizer and navigation -->
    <script src="js/performance-optimizer.js"></script>
    <script src="js/streamlined-nav.js"></script>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-robot"></i>
                Ruby AI Assistant
            </div>
            <nav class="nav">
                <!-- Navigation will be populated by streamlined-nav.js -->
            </nav>
        </div>
    </div>

    <div class="chat-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="quick-actions">
                <h3><i class="fas fa-brain"></i>AGI Actions</h3>
                <button class="action-btn" onclick="askQuestion('What should I buy right now based on Ruby algorithms?')">
                    🧠 Algorithm Recommendations
                </button>
                <button class="action-btn" onclick="askQuestion('Analyze all Project Ruby signals for best trades')">
                    🚀 Ruby Signal Analysis
                </button>
                <button class="action-btn" onclick="askQuestion('What would be a good bet today?')">
                    💎 Best Bet Today
                </button>
                <button class="action-btn" onclick="askQuestion('Show me Ruby trading performance and accuracy')">
                    📊 Algorithm Performance
                </button>
                <button class="action-btn" onclick="askQuestion('Portfolio allocation based on Ruby data')">
                    ⚖️ Smart Portfolio
                </button>
                <button class="action-btn" onclick="askQuestion('Market analysis using all Ruby algorithms')">
                    🔍 Deep Market Analysis
                </button>
            </div>

            <div class="crypto-stats">
                <h3><i class="fas fa-chart-line"></i>Live Stats</h3>
                <div class="stat-item">
                    <span>BTC Price:</span>
                    <span class="stat-value" id="btc-price">$93,747</span>
                </div>
                <div class="stat-item">
                    <span>Market Cap:</span>
                    <span class="stat-value">$1.85T</span>
                </div>
                <div class="stat-item">
                    <span>Fear & Greed:</span>
                    <span class="stat-value price-up">72 (Greed)</span>
                </div>
                <div class="stat-item">
                    <span>Active Coins:</span>
                    <span class="stat-value">13,000+</span>
                </div>
            </div>

            <div class="crypto-stats">
                <h3><i class="fas fa-brain"></i>AGI Status</h3>
                <div class="stat-item">
                    <span>Model:</span>
                    <span class="stat-value">Ruby AGI v2.0</span>
                </div>
                <div class="stat-item">
                    <span>Learning:</span>
                    <span class="stat-value">Self-Adaptive</span>
                </div>
                <div class="stat-item">
                    <span>Algorithm Accuracy:</span>
                    <span class="stat-value">84.7%</span>
                </div>
                <div class="stat-item">
                    <span>Patterns Found:</span>
                    <span class="stat-value" id="pattern-count">37</span>
                </div>
                <div class="stat-item">
                    <span>Learning Iterations:</span>
                    <span class="stat-value" id="learning-iterations">1,247</span>
                </div>
            </div>
        </div>

        <!-- Main Chat Area -->
        <div class="chat-main">
            <div class="chat-header">
                <div class="chat-title">
                    <i class="fas fa-robot"></i>
                    Ruby AI Assistant
                </div>
                <div class="chat-subtitle">
                    Ask me anything about cryptocurrency, blockchain, DeFi, trading, or market analysis!
                </div>
            </div>

            <div class="chat-messages" id="chat-messages">
                <!-- Welcome Message -->
                <div class="message ai">
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                        <div>
                            🧠 <strong>Welcome to Ruby AGI Assistant!</strong><br><br>
                            I'm your self-learning AGI that analyzes ALL of Project Ruby's algorithms to provide intelligent trading advice:
                            <br>• <strong>Algorithm Integration</strong>: Direct access to Ruby's trading signals
                            <br>• <strong>Self-Learning</strong>: Continuously improving from market data
                            <br>• <strong>Pattern Recognition</strong>: 37+ market patterns identified
                            <br>• <strong>Performance Tracking</strong>: 84.7% algorithm accuracy
                            <br>• <strong>Smart Recommendations</strong>: What to buy, when, and how much
                            <br>• <strong>Risk Assessment</strong>: Algorithm-based risk analysis
                            <br><br>
                            <span class="crypto-highlight">Ask me what to buy or what would be a good bet!</span> 🚀
                        </div>
                        <div class="message-time">Just now</div>
                    </div>
                </div>

                <!-- Typing Indicator -->
                <div class="message ai" id="typing-indicator" style="display: none;">
                    <div class="message-avatar">🤖</div>
                    <div class="typing-indicator">
                        <div class="typing-dots">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="chat-input">
                <div class="suggestions" id="suggestions">
                    <div class="suggestion" onclick="askQuestion('What should I buy based on Ruby algorithms?')">
                        Algorithm recommendations
                    </div>
                    <div class="suggestion" onclick="askQuestion('What would be a good bet today?')">
                        Best bet today
                    </div>
                    <div class="suggestion" onclick="askQuestion('Analyze Ruby trading performance')">
                        Algorithm accuracy
                    </div>
                    <div class="suggestion" onclick="askQuestion('Portfolio allocation advice')">
                        Smart portfolio
                    </div>
                </div>

                <div class="input-container">
                    <textarea
                        id="message-input"
                        class="input-field"
                        placeholder="Ask me what to buy or what would be a good bet... (e.g., 'What should I buy based on Ruby algorithms?' or 'Best trading opportunity today?')"
                        rows="1"
                    ></textarea>
                    <button class="send-btn" id="send-btn" onclick="sendMessage()">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Load FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Load AI Assistant Script -->
    <script src="js/ruby-ai-assistant.js"></script>
</body>
</html>
