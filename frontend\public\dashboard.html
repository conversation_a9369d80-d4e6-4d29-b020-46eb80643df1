<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Dashboard | Project Ruby - Turbo Mode</title>

    <!-- Performance optimizations -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="http://localhost:8004">
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">

    <!-- Critical CSS inline for faster loading -->
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; background: #0a0a0a; color: #fff; }
        .loading { display: flex; justify-content: center; align-items: center; height: 100vh; }
        .spinner { border: 3px solid #333; border-top: 3px solid #d4af37; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>

    <!-- Load performance optimizer first -->
    <script src="js/performance-optimizer.js"></script>
    <script src="js/turbo-startup.js"></script>

    <!-- Fonts with display swap for better performance -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Ruby theme -->
    <link rel="stylesheet" href="css/ruby-core.css">

    <!-- Icons with async loading -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media='all'">

    <!-- Core scripts with defer for better performance -->
    <script defer src="js/coin-config.js"></script>
    <script defer src="js/data-service.js"></script>
    <script defer src="js/ensure-consensus-nav.js"></script>
    <script defer src="js/update-navigation.js"></script>
    <style>
        .grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
        }
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        /* Custom styles for dashboard tables */
        .ruby-theme table {
            width: 100%;
            border-collapse: collapse;
        }

        .ruby-theme th {
            text-align: left;
            padding: 12px 15px;
            background-color: var(--ruby-bg-elevated);
            color: var(--ruby-text-primary);
            font-weight: 600;
            border-bottom: 1px solid var(--ruby-border);
        }

        .ruby-theme td {
            padding: 12px 15px;
            border-bottom: 1px solid var(--ruby-border);
            color: var(--ruby-text-primary);
        }

        .ruby-theme tr:hover {
            background-color: rgba(255, 255, 255, 0.03);
        }

        /* Asset list styling */
        .asset-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }

        .asset-item {
            display: flex;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid var(--ruby-border);
        }

        .asset-item:last-child {
            border-bottom: none;
        }

        .asset-name {
            color: var(--ruby-text-primary);
        }

        .asset-price {
            font-weight: 600;
            color: var(--ruby-gold);
        }

        /* Signal badges */
        .signal-badge {
            display: inline-block;
            padding: 4px 10px;
            border-radius: 4px;
            font-weight: 600;
            font-size: 12px;
            text-transform: uppercase;
        }

        .signal-buy {
            background-color: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
        }

        .signal-sell {
            background-color: rgba(244, 67, 54, 0.2);
            color: #F44336;
        }

        .signal-hold {
            background-color: rgba(255, 152, 0, 0.2);
            color: #FF9800;
        }

        /* View all links */
        .view-all {
            display: block;
            text-align: right;
            margin-top: 10px;
            color: var(--ruby-gold);
            font-size: 14px;
            font-weight: 500;
        }

        .view-all:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body class="ruby-theme">
    <!-- High-performance loading screen -->
    <div id="turbo-loading" class="loading">
        <div>
            <div class="spinner"></div>
            <h3 style="margin-top: 20px; color: #d4af37;">🚀 Turbo Loading...</h3>
            <p style="color: #888;">Initializing high-performance mode</p>
        </div>
    </div>

    <div class="container" style="display: none;" id="main-content">
        <header>
            <div>
                <div style="margin-bottom: 15px;">
                    <img src="images/project-ruby-text-logo.svg" alt="Project Ruby" style="height: 50px;" />
                </div>
                <h1>Trading Dashboard</h1>
                <div class="nav">
                    <a href="index.html">Home</a>
                    <a href="dashboard.html" style="font-weight: bold;">Dashboard</a>
                    <a href="trading.html">Trading</a>
                    <a href="governance.html">Signals</a>
                    <a href="sentiment.html">Sentiment</a>
                    <a href="predictions.html">Predictions</a>
                    <a href="charts.html">Charts</a>
                    <a href="multi-timeframe.html">Timeframes</a>
                    <a href="consensus.html">Consensus</a>
                    <a href="demo.html">Demo</a>
                    <a href="chatbot.html">Assistant</a>
                    <a href="cm.html">CM</a>
                    <a href="settings.html">Settings</a>
                </div>
            </div>

        </header>

        <div class="grid">
            <div>
                <div class="card">
                    <h2><i class="fas fa-signal"></i> Latest Trading Signals</h2>
                    <table id="trading-signals">
                        <thead>
                            <tr>
                                <th>Asset</th>
                                <th>Signal</th>
                                <th>Time Frame</th>
                                <th>Confidence</th>
                                <th>Price</th>
                                <th>Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>BTC (Bitcoin)</td>
                                <td><span class="signal-badge signal-buy">BUY</span></td>
                                <td>day</td>
                                <td>85%</td>
                                <td>$60,000.00</td>
                                <td>2023-04-21 14:30:00</td>
                            </tr>
                            <tr>
                                <td>ETH (Ethereum)</td>
                                <td><span class="signal-badge signal-buy">BUY</span></td>
                                <td>day</td>
                                <td>75%</td>
                                <td>$3,000.00</td>
                                <td>2023-04-21 14:30:00</td>
                            </tr>
                            <tr>
                                <td>UNI (Uniswap)</td>
                                <td><span class="signal-badge signal-hold">HOLD</span></td>
                                <td>day</td>
                                <td>65%</td>
                                <td>$5.00</td>
                                <td>2023-04-21 14:30:00</td>
                            </tr>
                        </tbody>
                    </table>
                    <a href="trading.html" class="view-all">View all trading signals →</a>
                </div>

                <div class="grid-2">
                    <div class="card">
                        <h2><i class="fas fa-chart-bar"></i> Market Signals</h2>
                        <table id="governance-signals">
                            <thead>
                                <tr>
                                    <th>Asset</th>
                                    <th>Type</th>
                                    <th>Strength</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>UNI</td>
                                    <td><span class="badge badge-blue">High Participation</span></td>
                                    <td>80%</td>
                                </tr>
                                <tr>
                                    <td>AAVE</td>
                                    <td><span class="badge badge-yellow">Contentious</span></td>
                                    <td>65%</td>
                                </tr>
                            </tbody>
                        </table>
                        <a href="governance.html" class="view-all">View all market signals →</a>
                    </div>

                    <div class="card">
                        <h2><i class="fas fa-comments"></i> Market Sentiment</h2>
                        <table id="sentiment-signals">
                            <thead>
                                <tr>
                                    <th>Asset</th>
                                    <th>Direction</th>
                                    <th>Strength</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>BTC</td>
                                    <td><span class="badge badge-green">Bullish</span></td>
                                    <td>75%</td>
                                </tr>
                                <tr>
                                    <td>ETH</td>
                                    <td><span class="badge badge-green">Bullish</span></td>
                                    <td>82%</td>
                                </tr>
                            </tbody>
                        </table>
                        <a href="sentiment.html" class="view-all">View all sentiment signals →</a>
                    </div>
                </div>
            </div>

            <div>
                <div class="card">
                    <h2><i class="fas fa-crown"></i> Subscription</h2>
                    <p>Current plan: <strong style="color: var(--ruby-gold);">Premium</strong></p>
                </div>

                <div class="card">
                    <h2><i class="fas fa-coins"></i> Available Assets</h2>
                    <ul class="asset-list">
                        <li class="asset-item">
                            <span class="asset-name">BTC (Bitcoin)</span>
                            <span class="asset-price">$60,000.00</span>
                        </li>
                        <li class="asset-item">
                            <span class="asset-name">ETH (Ethereum)</span>
                            <span class="asset-price">$3,000.00</span>
                        </li>
                        <li class="asset-item">
                            <span class="asset-name">UNI (Uniswap)</span>
                            <span class="asset-price">$5.00</span>
                        </li>
                        <li class="asset-item">
                            <span class="asset-name">AAVE (Aave)</span>
                            <span class="asset-price">$80.00</span>
                        </li>
                        <li class="asset-item">
                            <span class="asset-name">COMP (Compound)</span>
                            <span class="asset-price">$40.00</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="last-updated">
            Last updated: <span id="last-updated-time">2023-04-21 14:30:00</span>
        </div>
    </div>
    <!-- System initialization scripts -->
    <script src="js/data-service.js"></script>
    <script src="js/coin-config.js"></script>
    <script src="js/coin-sync.js"></script>
    <script src="js/add-coin-sync.js"></script>
    <script src="js/market-data-api.js"></script>
    <script src="js/signal-analysis.js"></script>
    <script src="js/sentiment-analysis.js"></script>
    <script src="js/system-status.js"></script>
    <script src="js/system-initializer.js"></script>
    <script src="js/startup.js"></script>

    <script>
        // DOM elements
        const tradingSignalsTableBody = document.querySelector('#trading-signals tbody');
        const governanceSignalsTableBody = document.querySelector('#governance-signals tbody');
        const sentimentSignalsTableBody = document.querySelector('#sentiment-signals tbody');
        const assetsListElement = document.querySelector('.asset-list');
        const lastUpdatedElement = document.createElement('div');

        // Add last updated element to the page
        lastUpdatedElement.style.textAlign = 'center';
        lastUpdatedElement.style.marginTop = '20px';
        lastUpdatedElement.style.color = '#757575';
        lastUpdatedElement.style.fontSize = '12px';
        document.querySelector('.container').appendChild(lastUpdatedElement);

        // Update last updated text
        function updateLastUpdated() {
            const now = new Date();
            lastUpdatedElement.textContent = `Last updated: ${now.toLocaleString()}`;

            // Update the last-updated-time element if it exists
            const lastUpdatedTimeElement = document.getElementById('last-updated-time');
            if (lastUpdatedTimeElement) {
                lastUpdatedTimeElement.textContent = now.toLocaleString();
            }
        }

        // Update trading signals
        function updateTradingSignals(signals, isSpecialMessage = false) {
            if (!tradingSignalsTableBody) return;

            let html = '';

            if (isSpecialMessage) {
                // Handle special messages (no signals or error)
                const signal = signals[0];

                if (signal.signal_type === 'none') {
                    // No signals message
                    html = `
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 20px;">
                            <div style="color: #757575;">
                                <i class="fas fa-info-circle" style="margin-right: 10px;"></i>
                                No active trading signals at this time. The system is analyzing market conditions but hasn't found any clear trading opportunities.
                            </div>
                        </td>
                    </tr>
                    `;
                } else if (signal.signal_type === 'error') {
                    // Error message
                    html = `
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 20px;">
                            <div style="color: #c62828;">
                                <i class="fas fa-exclamation-triangle" style="margin-right: 10px;"></i>
                                Error generating trading signals. Please try refreshing the page.
                            </div>
                        </td>
                    </tr>
                    `;
                }
            } else {
                // Normal signals
                signals.forEach(signal => {
                    const signalTypeClass =
                        signal.signal_type === 'buy' || signal.signal_type === 'long'
                            ? 'color: #2e7d32; font-weight: bold;'
                            : signal.signal_type === 'sell' || signal.signal_type === 'short'
                            ? 'color: #c62828; font-weight: bold;'
                            : signal.signal_type === 'hold'
                            ? 'color: #ff9800; font-weight: bold;'
                            : 'color: #757575; font-weight: bold;';

                    const signalTypeBadge =
                        signal.signal_type === 'buy' || signal.signal_type === 'long'
                            ? '<span class="signal-badge signal-buy">BUY</span>'
                            : signal.signal_type === 'sell' || signal.signal_type === 'short'
                            ? '<span class="signal-badge signal-sell">SELL</span>'
                            : signal.signal_type === 'hold'
                            ? '<span class="signal-badge signal-hold">HOLD</span>'
                            : signal.signal_type.toUpperCase();

                    html += `
                    <tr>
                        <td>${signal.asset_symbol} (${signal.asset_name})</td>
                        <td>${signalTypeBadge}</td>
                        <td>${signal.time_frame}</td>
                        <td>${Math.round(signal.confidence * 100)}%</td>
                        <td>$${signal.price.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                        <td>${new Date(signal.timestamp).toLocaleString()}</td>
                    </tr>
                    `;
                });
            }

            tradingSignalsTableBody.innerHTML = html;
            updateLastUpdated();
        }

        // Update governance signals
        function updateGovernanceSignals(signals) {
            if (!governanceSignalsTableBody) return;

            let html = '';
            signals.slice(0, 2).forEach(signal => {
                const badgeClass =
                    signal.type === 'high_participation' ? 'badge-blue' :
                    signal.type === 'contentious_proposal' ? 'badge-yellow' :
                    signal.type === 'high_impact_proposal' ? 'badge-red' : 'badge-blue';

                const typeName =
                    signal.type === 'high_participation' ? 'High Participation' :
                    signal.type === 'contentious_proposal' ? 'Contentious' :
                    signal.type === 'high_impact_proposal' ? 'High Impact' : signal.type;

                html += `
                <tr>
                    <td>${signal.asset}</td>
                    <td><span class="badge ${badgeClass}">${typeName}</span></td>
                    <td>${Math.round(signal.strength * 100)}%</td>
                </tr>
                `;
            });

            governanceSignalsTableBody.innerHTML = html;
        }

        // Update sentiment signals
        function updateSentimentSignals(signals) {
            if (!sentimentSignalsTableBody) return;

            let html = '';
            signals.slice(0, 2).forEach(signal => {
                html += `
                <tr>
                    <td>${signal.asset}</td>
                    <td><span class="badge badge-${signal.direction === 'bullish' ? 'green' : 'red'}">${signal.direction}</span></td>
                    <td>${Math.round(signal.strength * 100)}%</td>
                </tr>
                `;
            });

            sentimentSignalsTableBody.innerHTML = html;
        }

        // Update assets list
        function updateAssets(assets) {
            if (!assetsListElement) return;

            let html = '';
            assets.slice(0, 5).forEach(asset => {
                html += `
                <li class="asset-item">
                    <span class="asset-name">${asset.symbol} (${asset.name})</span>
                    <span class="asset-price">$${asset.price.toFixed(2)}</span>
                </li>
                `;
            });

            assetsListElement.innerHTML = html;
        }

        // Initialize dashboard data
        async function initializeDashboardData() {
            try {
                console.log('Initializing dashboard data...');

                // Listen for system-ready event
                window.addEventListener('system-ready', async () => {
                    console.log('System is ready, loading dashboard data...');

                    try {
                        // Get active coins
                        const activeCoins = CoinConfigService.getActiveCoins();

                        // Update assets list
                        const assets = [];
                        for (const coin of activeCoins) {
                            try {
                                const priceData = await MarketDataAPI.fetchPriceData(coin.symbol);
                                assets.push({
                                    symbol: coin.symbol,
                                    name: coin.name,
                                    price: priceData.price
                                });
                            } catch (error) {
                                console.error(`Error fetching price for ${coin.symbol}:`, error);
                            }
                        }
                        updateAssets(assets);

                        // Generate trading signals
                        if (typeof SignalAnalysisService !== 'undefined') {
                            try {
                                console.log('Generating real-time trading signals...');
                                const signals = await SignalAnalysisService.generateSignals(activeCoins);

                                // Format signals for dashboard display
                                const formattedSignals = signals.map(signal => ({
                                    asset_symbol: signal.coin.symbol,
                                    asset_name: signal.coin.name,
                                    signal_type: signal.type,
                                    time_frame: signal.timeFrame,
                                    confidence: signal.confidence / 100,
                                    price: signal.currentPrice,
                                    timestamp: signal.timestamp
                                }));

                                console.log(`Generated ${formattedSignals.length} trading signals`);

                                // If no signals were generated, show a "No Signals" message
                                if (formattedSignals.length === 0) {
                                    const noSignalMessage = [{
                                        asset_symbol: 'N/A',
                                        asset_name: 'No Active Signals',
                                        signal_type: 'none',
                                        time_frame: 'N/A',
                                        confidence: 0,
                                        price: 0,
                                        timestamp: new Date().toISOString()
                                    }];
                                    updateTradingSignals(noSignalMessage, true);
                                } else {
                                    updateTradingSignals(formattedSignals, false);
                                }
                            } catch (error) {
                                console.error('Error generating trading signals:', error);
                                // Show error message in the signals table
                                const errorMessage = [{
                                    asset_symbol: 'ERROR',
                                    asset_name: 'Signal Generation Failed',
                                    signal_type: 'error',
                                    time_frame: 'N/A',
                                    confidence: 0,
                                    price: 0,
                                    timestamp: new Date().toISOString()
                                }];
                                updateTradingSignals(errorMessage, true);
                            }
                        }

                        // Generate sentiment signals
                        if (typeof SentimentAnalysisService !== 'undefined') {
                            const sentimentSignals = await SentimentAnalysisService.generateSentimentSignals(activeCoins);
                            updateSentimentSignals(sentimentSignals);
                        }

                        // Update governance signals (using sample data for now)
                        if (typeof DataService !== 'undefined') {
                            const governanceSignals = await DataService.fetchData(DataService.API_ENDPOINTS.GOVERNANCE_SIGNALS);
                            updateGovernanceSignals(governanceSignals);
                        }

                        // Set up event listeners for real-time updates
                        window.addEventListener('price-data-updated', async () => {
                            const updatedAssets = [];
                            for (const coin of activeCoins) {
                                try {
                                    const priceData = await MarketDataAPI.fetchPriceData(coin.symbol);
                                    updatedAssets.push({
                                        symbol: coin.symbol,
                                        name: coin.name,
                                        price: priceData.price
                                    });
                                } catch (error) {
                                    console.error(`Error fetching price for ${coin.symbol}:`, error);
                                }
                            }
                            updateAssets(updatedAssets);
                            updateLastUpdated();
                        });

                        window.addEventListener('signals-updated', (event) => {
                            const signals = event.detail.signals;

                            // Format signals for dashboard display
                            if (signals && signals.length > 0) {
                                const formattedSignals = signals.map(signal => ({
                                    asset_symbol: signal.coin.symbol,
                                    asset_name: signal.coin.name,
                                    signal_type: signal.type,
                                    time_frame: signal.timeFrame,
                                    confidence: signal.confidence / 100,
                                    price: signal.currentPrice,
                                    timestamp: signal.timestamp
                                }));
                                updateTradingSignals(formattedSignals, false);
                            } else {
                                // No signals
                                const noSignalMessage = [{
                                    asset_symbol: 'N/A',
                                    asset_name: 'No Active Signals',
                                    signal_type: 'none',
                                    time_frame: 'N/A',
                                    confidence: 0,
                                    price: 0,
                                    timestamp: new Date().toISOString()
                                }];
                                updateTradingSignals(noSignalMessage, true);
                            }

                            updateLastUpdated();
                        });

                        window.addEventListener('sentiment-updated', (event) => {
                            updateSentimentSignals(event.detail.sentimentSignals);
                            updateLastUpdated();
                        });

                        console.log('Dashboard data initialized successfully');
                    } catch (error) {
                        console.error('Error initializing dashboard data:', error);
                    }
                });

                // Initialize the system
                if (typeof SystemInitializer !== 'undefined') {
                    await SystemInitializer.initializeSystem();
                } else {
                    console.error('SystemInitializer not available');
                }
            } catch (error) {
                console.error('Error initializing dashboard:', error);
            }
        }

        // Start the dashboard when the page loads
        window.addEventListener('DOMContentLoaded', initializeDashboardData);
    </script>
    <script>
        // DOM elements
        const tradingSignalsTableBody = document.querySelector('#trading-signals tbody');
        const governanceSignalsTableBody = document.querySelector('#governance-signals tbody');
        const sentimentSignalsTableBody = document.querySelector('#sentiment-signals tbody');
        const lastUpdatedElement = document.getElementById('last-updated-time');

        // Update last updated text
        function updateLastUpdated() {
            const now = new Date();
            if (lastUpdatedElement) {
                lastUpdatedElement.textContent = now.toLocaleString();
            }
        }

        // Update trading signals
        function updateTradingSignals(signals) {
            if (!tradingSignalsTableBody) return;

            let html = '';
            signals.forEach(signal => {
                html += `
                <tr>
                    <td>${signal.asset_symbol}</td>
                    <td><span class="signal-badge signal-${signal.signal_type === 'buy' ? 'buy' : signal.signal_type === 'sell' ? 'sell' : 'hold'}">${signal.signal_type}</span></td>
                    <td>${signal.time_frame}</td>
                    <td>${Math.round(signal.confidence * 100)}%</td>
                    <td>$${signal.price.toLocaleString()}</td>
                </tr>
                `;
            });

            tradingSignalsTableBody.innerHTML = html;
            updateLastUpdated();
        }

        // Update governance signals
        function updateGovernanceSignals(signals) {
            if (!governanceSignalsTableBody) return;

            let html = '';
            signals.forEach(signal => {
                const dotColor =
                    signal.type === 'high_participation' ? '#1565c0' :
                    signal.type === 'contentious_proposal' ? '#e65100' :
                    signal.type === 'high_impact_proposal' ? '#c62828' : '#757575';

                const badgeClass =
                    signal.type === 'high_participation' ? 'badge-blue' :
                    signal.type === 'contentious_proposal' ? 'badge-orange' :
                    signal.type === 'high_impact_proposal' ? 'badge-red' : 'badge-blue';

                const typeName =
                    signal.type === 'high_participation' ? 'High Participation' :
                    signal.type === 'contentious_proposal' ? 'Contentious' :
                    signal.type === 'high_impact_proposal' ? 'High Impact' : signal.type;

                html += `
                <tr>
                    <td>${signal.asset}</td>
                    <td>
                        <div style="display: flex; align-items: center;">
                            <span class="dot" style="background-color: ${dotColor};"></span>
                            <span class="badge ${badgeClass}">${typeName}</span>
                        </div>
                    </td>
                    <td>${signal.description}</td>
                    <td>${Math.round(signal.strength * 100)}%</td>
                </tr>
                `;
            });

            governanceSignalsTableBody.innerHTML = html;
        }

        // Update sentiment signals
        function updateSentimentSignals(signals) {
            if (!sentimentSignalsTableBody) return;

            let html = '';
            signals.forEach(signal => {
                html += `
                <tr>
                    <td>${signal.asset}</td>
                    <td><span class="badge badge-${signal.direction === 'bullish' ? 'green' : 'red'}">${signal.direction}</span></td>
                    <td>${signal.type.replace('_', ' ')}</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill ${signal.direction === 'bullish' ? 'green' : 'red'}" style="width: ${signal.strength * 100}%;"></div>
                        </div>
                    </td>
                </tr>
                `;
            });

            sentimentSignalsTableBody.innerHTML = html;
        }

        // Update crypto prices
        function updateCryptoPrices(data) {
            // Extract price data from the API response
            const priceData = data.map(item => ({
                symbol: item.content.symbol.toUpperCase(),
                price: item.content.price_usd
            }));

            // Update price displays in the UI
            priceData.forEach(item => {
                const priceElements = document.querySelectorAll(`.price-${item.symbol}`);
                priceElements.forEach(el => {
                    el.textContent = `$${item.price.toLocaleString()}`;
                });
            });
        }

        // Initialize data and set up refresh intervals
        async function initializeData() {
            try {
                // Authenticate first
                await DataService.authenticate();

                // Set up refresh intervals for different data types
                DataService.setupRefresh(
                    DataService.API_ENDPOINTS.TRADING_SIGNALS,
                    updateTradingSignals,
                    DataService.REFRESH_INTERVALS.TRADING_SIGNALS
                );

                DataService.setupRefresh(
                    DataService.API_ENDPOINTS.GOVERNANCE_SIGNALS,
                    updateGovernanceSignals,
                    DataService.REFRESH_INTERVALS.GOVERNANCE
                );

                DataService.setupRefresh(
                    DataService.API_ENDPOINTS.SENTIMENT_SIGNALS,
                    updateSentimentSignals,
                    DataService.REFRESH_INTERVALS.SENTIMENT
                );

                DataService.setupRefresh(
                    DataService.API_ENDPOINTS.CRYPTO_PRICES,
                    updateCryptoPrices,
                    DataService.REFRESH_INTERVALS.PRICES
                );

                console.log('All data refresh intervals set up successfully');
            } catch (error) {
                console.error('Error initializing data:', error);
            }
        }

        // Start the application when the page loads
        window.addEventListener('DOMContentLoaded', initializeData);
    </script>

    <!-- Turbo initialization script -->
    <script>
        // Hide loading screen and show content when turbo startup completes
        window.addEventListener('turbo-startup-complete', (event) => {
            const loadingScreen = document.getElementById('turbo-loading');
            const mainContent = document.getElementById('main-content');

            // Smooth transition
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
                mainContent.style.display = 'block';
                mainContent.style.opacity = '0';

                // Fade in main content
                requestAnimationFrame(() => {
                    mainContent.style.transition = 'opacity 0.3s ease-in-out';
                    mainContent.style.opacity = '1';
                });

                // Show performance metrics
                const { loadTime, loadedModules, criticalData } = event.detail;
                console.log(`🎉 Dashboard loaded in ${loadTime.toFixed(2)}ms`);
                console.log('📊 Performance metrics:', {
                    loadTime: loadTime + 'ms',
                    modules: loadedModules.length,
                    dataItems: Object.keys(criticalData).length
                });

                // Update dashboard with preloaded data
                if (criticalData.signals && criticalData.signals.length > 0) {
                    updateTradingSignals(criticalData.signals);
                }

                // Show success notification
                if (loadTime < 2000) {
                    const notification = document.createElement('div');
                    notification.style.cssText = `
                        position: fixed; top: 20px; right: 20px;
                        background: #4CAF50; color: white; padding: 15px 20px;
                        border-radius: 8px; z-index: 1000; font-weight: bold;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    `;
                    notification.innerHTML = `🚀 Turbo Mode: ${loadTime.toFixed(0)}ms load time!`;
                    document.body.appendChild(notification);

                    setTimeout(() => {
                        notification.style.opacity = '0';
                        setTimeout(() => notification.remove(), 300);
                    }, 3000);
                }
            }, 300);
        });

        // Fallback: show content after 5 seconds if turbo startup doesn't complete
        setTimeout(() => {
            const loadingScreen = document.getElementById('turbo-loading');
            const mainContent = document.getElementById('main-content');

            if (loadingScreen.style.display !== 'none') {
                console.warn('Turbo startup timeout, showing content anyway');
                loadingScreen.style.display = 'none';
                mainContent.style.display = 'block';
            }
        }, 5000);

        // Performance monitoring
        if (typeof PerformanceOptimizer !== 'undefined') {
            setInterval(() => {
                const metrics = PerformanceOptimizer.getMetrics();
                if (metrics.loadTime > 0) {
                    document.title = `Dashboard | Ruby (${metrics.loadTime.toFixed(0)}ms)`;
                }
            }, 10000);
        }
    </script>
</body>
</html>
