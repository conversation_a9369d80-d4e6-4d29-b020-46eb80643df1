<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Charts | Project Ruby</title>
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Ruby theme -->
    <link rel="stylesheet" href="css/ruby-core.css">
    <!-- TradingView Chart Fixes -->
    <link rel="stylesheet" href="css/tradingview-fix.css">
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Original styles (preserved for functionality) -->
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .nav {
            display: flex;
            gap: 20px;
        }
        .nav a {
            color: #0066cc;
            text-decoration: none;
        }
        .nav a:hover {
            text-decoration: underline;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-red {
            background-color: #f44336;
            color: white;
        }
    </style>
    <style>
        /* Live Charts Page Styles */
        .chart-container {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        /* Tab Navigation Styles */
        .tabs-container {
            margin-bottom: 20px;
        }

        .tabs-nav {
            display: flex;
            border-bottom: 2px solid #e0e0e0;
            margin-bottom: 20px;
        }

        .tab-button {
            padding: 10px 20px;
            background: none;
            border: none;
            border-bottom: 2px solid transparent;
            margin-bottom: -2px;
            font-size: 16px;
            font-weight: 500;
            color: #666;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
        }

        .tab-button i {
            margin-right: 8px;
        }

        .tab-button:hover {
            color: #1976d2;
        }

        .tab-button.active {
            color: #1976d2;
            border-bottom-color: #1976d2;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Trading Signals Styles */
        .timeframe-tabs {
            display: flex;
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 20px;
        }

        .timeframe-button {
            padding: 8px 16px;
            background: none;
            border: none;
            border-radius: 4px;
            margin-right: 10px;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            cursor: pointer;
            transition: all 0.3s;
        }

        .timeframe-button:hover {
            background-color: #e0e0e0;
        }

        .timeframe-button.active {
            background-color: #1976d2;
            color: white;
        }

        .signals-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .signal-card {
            background-color: #1f1f1f !important;
            color: #ffffff !important;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-left: 4px solid #1976d2;
        }

        .signal-card.long {
            border-left-color: #4caf50;
        }

        .signal-card.short {
            border-left-color: #f44336;
        }

        .signal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .asset-name {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            color: #d4af37 !important; /* Gold for asset name */
        }

        .current-price {
            font-size: 16px;
            font-weight: 500;
            color: #e0e0e0 !important;
        }

        .signal-body {
            margin-bottom: 15px;
        }

        .signal-direction {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .signal-direction.long {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        .signal-direction.short {
            background-color: #ffebee;
            color: #c62828;
        }

        .signal-strength {
            font-size: 14px;
            color: #e0e0e0 !important;
            margin-bottom: 10px;
        }

        .signal-indicators {
            background-color: #2a2a2a !important;
            border-radius: 8px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            border: 1px solid #333333;
        }

        .indicator-section {
            padding-bottom: 8px;
        }

        .indicator-section:not(:last-child) {
            border-bottom: 1px solid #333333;
        }

        .indicator-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #d4af37 !important; /* Gold for section headings */
            font-size: 14px;
        }

        .indicator {
            margin-bottom: 5px;
            font-size: 14px;
            color: #e0e0e0 !important;
        }

        .indicator:last-child {
            margin-bottom: 0;
        }

        .health-status, .trend {
            font-weight: 500;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }

        .health-status.very_healthy, .trend.very_bullish {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        .health-status.healthy, .trend.bullish {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        .health-status.neutral, .trend.neutral {
            background-color: #e3f2fd;
            color: #1565c0;
        }

        .health-status.concerning, .trend.bearish {
            background-color: #fff8e1;
            color: #f57c00;
        }

        .health-status.unhealthy, .trend.very_bearish {
            background-color: #ffebee;
            color: #c62828;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.4);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 0;
            border: 1px solid #888;
            border-radius: 8px;
            width: 80%;
            max-width: 1000px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
            padding: 15px 20px;
            background-color: #1976d2;
            color: white;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 20px;
            font-weight: 500;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 15px;
        }

        .analysis-container {
            margin-top: 30px;
        }

        .analysis-container h3 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 18px;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .analysis-card {
            background-color: #f9f9f9;
            border-radius: 6px;
            padding: 15px;
            border-left: 3px solid #1976d2;
        }

        .analysis-card h4 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 14px;
            color: #555;
        }

        .analysis-card div:nth-child(2) {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 8px;
            color: #333;
        }

        .analysis-description {
            font-size: 13px;
            color: #666;
            line-height: 1.4;
        }

        /* View History Link */
        .view-history-link {
            color: #d4af37 !important; /* Gold for links */
            text-decoration: none;
            font-size: 13px;
            display: inline-flex;
            align-items: center;
        }

        .view-history-link:hover {
            text-decoration: underline;
        }

        /* On-Chain Analysis Styles */
        .onchain-analysis {
            margin-top: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
        }

        .onchain-analysis h3 {
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 18px;
            color: #333;
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .analysis-card {
            background-color: white;
            border-radius: 6px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border-left: 3px solid #1976d2;
        }

        .analysis-card h4 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 14px;
            color: #555;
        }

        .analysis-card div:nth-child(2) {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 8px;
            color: #333;
        }

        #onchain-chart-container {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Last Updated Styles */
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .section-header h3 {
            margin: 0;
        }

        .chart-header {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 10px;
        }

        .last-updated-container {
            font-size: 12px;
            color: #e0e0e0;
            font-style: italic;
        }

        .chart-info .last-updated-container {
            color: #ffffff;
        }

        .last-updated-label {
            font-weight: 500;
        }

        .last-updated-time {
            margin-left: 5px;
        }

        .signal-footer {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 14px;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #333333;
        }

        .entry-point {
            grid-column: 1 / -1;
            font-weight: 500;
            color: #e0e0e0 !important;
        }

        .take-profit {
            color: #2e7d32;
        }

        .stop-loss {
            color: #c62828;
        }

        .chart-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 15px;
        }

        .control-group {
            display: flex;
            flex-direction: column;
        }

        .control-group label {
            margin-bottom: 5px;
            font-weight: 500;
            display: flex;
            align-items: center;
            font-size: 14px;
        }

        .control-group label i {
            margin-right: 5px;
            color: var(--ruby-gold);
        }

        .chart-info-icon {
            color: var(--ruby-gold);
            margin-right: 5px;
        }

        .control-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            min-width: 180px;
        }

        .control-group select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
            outline: none;
        }

        #trading-chart-container {
            height: 600px;
            width: 100%;
            border-radius: 4px;
            overflow: hidden;
            background-color: #1e222d;
            position: relative;
        }

        .chart-info {
            background-color: #005b96;
            border-left: 4px solid var(--ruby-gold);
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
            color: #ffffff;
        }

        .chart-info h3 {
            margin-top: 0;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            color: #ffffff;
        }

        .chart-info h3 i {
            margin-right: 8px;
            color: var(--ruby-gold);
        }

        .chart-info p {
            margin: 0 0 10px 0;
            color: #ffffff;
        }

        .chart-info ul {
            margin: 0;
            padding-left: 20px;
        }

        .chart-info li {
            margin-bottom: 5px;
            color: #ffffff;
        }

        .chart-info li:last-child {
            margin-bottom: 0;
        }

        .chart-info strong,
        .chart-info b {
            color: #ffffff;
            font-weight: 700;
        }

        .chart-info em,
        .chart-info i:not(.fas):not(.far):not(.fab) {
            color: #ffffff;
            font-style: italic;
        }

        .chart-info a {
            color: var(--ruby-gold);
            text-decoration: underline;
            font-weight: 500;
        }

        .chart-info a:hover {
            color: #ffffff;
            text-decoration: underline;
        }

        .chart-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .feature-card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 18px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-top: 3px solid var(--ruby-gold);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            background-color: white;
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .feature-card h4 {
            margin-top: 0;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: 700;
            color: #000000;
        }

        .feature-card h4 i {
            margin-right: 8px;
            color: var(--ruby-gold);
            font-size: 18px;
        }

        .feature-card p {
            margin: 0;
            color: rgba(0, 0, 0, 0.8);
            font-size: 14px;
            line-height: 1.5;
        }

        /* Loading state */
        .chart-loading, .chart-error {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 600px;
            background-color: #1e222d;
            color: #e0e0e0;
            border-radius: 4px;
            text-align: center;
            padding: 20px;
        }

        .chart-error h3 {
            margin-bottom: 10px;
            color: #e0e0e0;
        }

        .chart-error p {
            max-width: 400px;
            margin-bottom: 20px;
            color: #b0b0b0;
        }

        .btn-primary {
            background-color: var(--ruby-gold);
            color: #000;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
        }

        .btn-primary:hover {
            background-color: #d4af37;
        }

        .loading-spinner {
            border: 4px solid rgba(212, 175, 55, 0.2);
            border-top: 4px solid var(--ruby-gold);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .chart-controls {
                flex-direction: column;
                gap: 10px;
            }

            #trading-chart-container {
                height: 400px;
            }
        }
    </style>
</head>
<body class="ruby-theme">
    <div class="container">
        <header>
            <div>

                <h1>Trading Charts</h1>
                <div class="nav">
                    <a href="index.html">Home</a>
                    <a href="dashboard.html">Dashboard</a>
                    <a href="trading-hub.html">Trading</a>
                    <a href="charts.html" style="font-weight: bold;">Charts</a>
                    <a href="predictions.html">Predictions</a>
                    <a href="chatbot.html">Assistant</a>
                    <a href="settings.html">Settings</a>
                </div>
            </div>

        </header>

        <div class="tabs-container">
            <div class="tabs-nav">
                <button class="tab-button active" data-tab="charts-tab"><i class="fas fa-chart-line"></i> Live Charts</button>
                <button class="tab-button" data-tab="signals-tab"><i class="fas fa-signal"></i> Trading Signals</button>
                <button class="tab-button" data-tab="onchain-tab"><i class="fas fa-link"></i> On-Chain Data</button>
            </div>

            <!-- Charts Tab Content -->
            <div id="charts-tab" class="tab-content active">
                <div class="chart-info">
                    <div class="section-header">
                        <h3><i class="fas fa-chart-line"></i> Professional Trading Charts</h3>
                        <div class="last-updated-container">
                            <span class="last-updated-label">Last updated:</span>
                            <span class="last-updated-time" data-component="live-charts">Just now</span>
                        </div>
                    </div>
                    <p>
                        Access real-time trading charts with advanced technical analysis tools:
                    </p>
                    <ul>
                        <li><strong>Multiple Timeframes:</strong> From 1-minute to monthly charts</li>
                        <li><strong>Technical Indicators:</strong> Moving averages, RSI, MACD, Bollinger Bands, and more</li>
                        <li><strong>Drawing Tools:</strong> Trend lines, Fibonacci retracements, and support/resistance levels</li>
                        <li><strong>Real-time Data:</strong> Live price updates from major exchanges</li>
                    </ul>
                    <p class="mt-1"><em>Use the controls above the chart to customize your view and analysis.</em></p>
                </div>

                <div class="chart-controls">
                    <div class="control-group">
                        <label for="coin-selector"><i class="fas fa-coins"></i> Cryptocurrency</label>
                        <select id="coin-selector">
                            <option value="BTCUSD">Bitcoin (BTC)</option>
                            <option value="ETHUSD">Ethereum (ETH)</option>
                            <option value="SOLUSD">Solana (SOL)</option>
                            <option value="ADAUSD">Cardano (ADA)</option>
                            <option value="DOTUSD">Polkadot (DOT)</option>
                            <option value="AVAXUSD">Avalanche (AVAX)</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label for="timeframe-selector"><i class="fas fa-clock"></i> Timeframe</label>
                        <select id="timeframe-selector">
                            <option value="1">1 Minute</option>
                            <option value="5">5 Minutes</option>
                            <option value="15">15 Minutes</option>
                            <option value="30">30 Minutes</option>
                            <option value="60">1 Hour</option>
                            <option value="240">4 Hours</option>
                            <option value="D" selected>1 Day</option>
                            <option value="W">1 Week</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label for="exchange-selector"><i class="fas fa-building"></i> Exchange</label>
                        <select id="exchange-selector">
                            <option value="BINANCE" selected>Binance</option>
                            <option value="COINBASE">Coinbase</option>
                            <option value="KRAKEN">Kraken</option>
                            <option value="FTX">FTX</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label for="chart-type"><i class="fas fa-chart-bar"></i> Chart Type</label>
                        <select id="chart-type">
                            <option value="1" selected>Candles</option>
                            <option value="2">Hollow Candles</option>
                            <option value="3">Heikin Ashi</option>
                            <option value="4">Line</option>
                            <option value="5">Area</option>
                            <option value="6">Bars</option>
                        </select>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-header">
                        <div class="last-updated-container">
                            <span class="last-updated-label">Chart data updated:</span>
                            <span class="last-updated-time" data-component="chart-data">Just now</span>
                        </div>
                    </div>
                    <div id="trading-chart-container" style="height: 600px; width: 100%; position: relative;">
                        <div class="chart-loading">
                            <div class="loading-spinner"></div>
                            <span style="color: #e0e0e0; font-size: 16px; margin-top: 15px;">Loading chart data...</span>
                            <p style="color: #b0b0b0; margin-top: 10px; max-width: 400px; text-align: center;">
                                Please wait while we load the latest market data for your selected cryptocurrency.
                                This chart provides real-time price information and technical analysis tools.
                            </p>
                        </div>
                    </div>
                    <div class="chart-description" style="margin-top: 15px; background-color: #1e222d; padding: 15px; border-radius: 8px; color: #b0b0b0;">
                        <h4 style="color: #e0e0e0; margin-bottom: 10px;">About This Chart</h4>
                        <p>This interactive chart displays real-time price data for your selected cryptocurrency. You can:</p>
                        <ul style="margin-top: 8px; margin-left: 20px;">
                            <li>Change the timeframe using the dropdown above</li>
                            <li>Switch between different cryptocurrencies</li>
                            <li>Select different chart types (candles, lines, etc.)</li>
                            <li>Draw on the chart using the built-in tools</li>
                            <li>Add technical indicators from the toolbar</li>
                        </ul>
                        <p style="margin-top: 10px;">The chart updates automatically with the latest market data.</p>
                    </div>
                </div>
            </div>

            <!-- Trading Signals Tab Content -->
            <div id="signals-tab" class="tab-content">
                <div class="chart-info">
                    <div class="section-header">
                        <h3><i class="fas fa-signal"></i> Timeframe-Based Trading Signals</h3>
                        <div class="last-updated-container">
                            <span class="last-updated-label">Last updated:</span>
                            <span class="last-updated-time" data-component="trading-signals">Just now</span>
                        </div>
                    </div>
                    <p>
                        Get specific trading signals for different timeframes to optimize your trading strategy:
                    </p>
                    <ul>
                        <li><strong>1 Hour Signals:</strong> Ultra short-term trading opportunities for day traders</li>
                        <li><strong>2 Hour Signals:</strong> Short-term signals with higher accuracy than 1-hour timeframe</li>
                        <li><strong>4 Hour Signals:</strong> Medium-term signals with balanced risk/reward ratio</li>
                        <li><strong>8 Hour Signals:</strong> Longer-term signals for swing traders with reduced noise</li>
                    </ul>
                    <p class="mt-1"><em>Select a timeframe below to view trading signals for major cryptocurrencies.</em></p>
                </div>

                <div class="timeframe-tabs">
                    <button class="timeframe-button active" data-timeframe="1h">1 Hour</button>
                    <button class="timeframe-button" data-timeframe="2h">2 Hours</button>
                    <button class="timeframe-button" data-timeframe="4h">4 Hours</button>
                    <button class="timeframe-button" data-timeframe="8h">8 Hours</button>
                </div>

                <div class="signals-container" id="signals-1h">
                    <div class="loading-spinner"></div>
                    <p style="text-align: center; color: #e0e0e0;">Loading real-time trading signals...</p>
                </div>

                <!-- Other timeframe containers (hidden by default) -->
                <div class="signals-container" id="signals-2h" style="display: none;">
                    <div class="loading-spinner"></div>
                    <p style="text-align: center; color: #e0e0e0;">Loading real-time trading signals...</p>
                </div>

                <!-- 4-hour timeframe container -->
                <div class="signals-container" id="signals-4h" style="display: none;">
                    <div class="loading-spinner"></div>
                    <p style="text-align: center; color: #e0e0e0;">Loading real-time trading signals...</p>
                </div>

                <!-- 8-hour timeframe container -->
                <div class="signals-container" id="signals-8h" style="display: none;">
                    <div class="loading-spinner"></div>
                    <p style="text-align: center; color: #e0e0e0;">Loading real-time trading signals...</p>
                </div>
            </div>

            <!-- On-Chain Data Tab Content -->
            <div id="onchain-tab" class="tab-content">
                <div class="chart-info">
                    <div class="section-header">
                        <h3><i class="fas fa-link"></i> On-Chain Metrics Analysis</h3>
                        <div class="last-updated-container">
                            <span class="last-updated-label">Last updated:</span>
                            <span class="last-updated-time" data-component="onchain-data">Just now</span>
                        </div>
                    </div>
                    <p>
                        Analyze blockchain data to gain deeper insights into cryptocurrency markets:
                    </p>
                    <ul>
                        <li><strong>Network Activity:</strong> Track active addresses, transaction volume, and network usage</li>
                        <li><strong>Exchange Flows:</strong> Monitor deposits and withdrawals from major exchanges</li>
                        <li><strong>Whale Movements:</strong> Track large holder behavior and concentration</li>
                        <li><strong>Mining/Staking:</strong> Analyze network security and validator activity</li>
                    </ul>
                    <p class="mt-1"><em>Select a cryptocurrency and metrics below to visualize on-chain data.</em></p>
                </div>

                <div class="chart-controls">
                    <div class="control-group">
                        <label for="onchain-coin-selector"><i class="fas fa-coins"></i> Cryptocurrency</label>
                        <select id="onchain-coin-selector">
                            <option value="BTC" selected>Bitcoin (BTC)</option>
                            <option value="ETH">Ethereum (ETH)</option>
                            <option value="SOL">Solana (SOL)</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label for="onchain-metric-selector"><i class="fas fa-chart-bar"></i> Primary Metric</label>
                        <select id="onchain-metric-selector">
                            <option value="active_addresses" selected>Active Addresses</option>
                            <option value="transaction_volume">Transaction Volume</option>
                            <option value="transaction_count">Transaction Count</option>
                            <option value="exchange_flow">Exchange Flow</option>
                            <option value="mining_hashrate">Mining Hashrate</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label for="onchain-timeframe-selector"><i class="fas fa-calendar"></i> Timeframe</label>
                        <select id="onchain-timeframe-selector">
                            <option value="7">7 Days</option>
                            <option value="30" selected>30 Days</option>
                            <option value="90">90 Days</option>
                            <option value="180">180 Days</option>
                        </select>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-header">
                        <div class="last-updated-container">
                            <span class="last-updated-label">On-chain data updated:</span>
                            <span class="last-updated-time" data-component="onchain-chart-data">Just now</span>
                        </div>
                    </div>
                    <div id="onchain-chart-container" style="background-color: #1e222d; color: #e0e0e0; padding: 20px; border-radius: 8px;">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <h3 style="color: #e0e0e0; margin-bottom: 10px;">Cryptocurrency Fear & Greed Index</h3>
                            <p style="color: #b0b0b0;">This index represents market sentiment, ranging from extreme fear to extreme greed.</p>
                        </div>
                        <img src="https://alternative.me/crypto/fear-and-greed-index.png" alt="On-Chain Chart" style="width: 100%; max-height: 500px; object-fit: contain; border-radius: 8px;">
                        <div style="margin-top: 20px; color: #b0b0b0; text-align: center;">
                            <p>The Fear & Greed Index is calculated based on volatility, market momentum/volume, social media, surveys, bitcoin dominance, and google trends.</p>
                            <p>Values closer to 0 indicate <span style="color: #f44336;">Extreme Fear</span>, while values closer to 100 indicate <span style="color: #4caf50;">Extreme Greed</span>.</p>
                        </div>
                    </div>
                </div>

                <div class="onchain-analysis">
                    <h3>Advanced On-Chain Analysis</h3>
                    <div class="analysis-grid">
                        <div class="analysis-card">
                            <h4>Correlation with Price</h4>
                            <div id="correlation-value">0.75 (Strong Positive)</div>
                            <div class="analysis-description">This metric has a strong positive correlation with price movements over the selected timeframe.</div>
                        </div>
                        <div class="analysis-card">
                            <h4>Trend Analysis</h4>
                            <div id="trend-value">Upward (Bullish)</div>
                            <div class="analysis-description">The metric shows a consistent upward trend, which is typically bullish for price.</div>
                        </div>
                        <div class="analysis-card">
                            <h4>Anomaly Detection</h4>
                            <div id="anomaly-value">No anomalies detected</div>
                            <div class="analysis-description">No unusual patterns or outliers detected in the recent data.</div>
                        </div>
                        <div class="analysis-card">
                            <h4>Predictive Power</h4>
                            <div id="predictive-value">High (85%)</div>
                            <div class="analysis-description">This metric has shown high predictive power for price movements in the past.</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="chart-features">
            <div class="feature-card">
                <h4><i class="fas fa-robot"></i> AI-Powered Analysis</h4>
                <p>Our machine learning models analyze chart patterns and market conditions to identify potential trading opportunities and key support/resistance levels.</p>
            </div>
            <div class="feature-card">
                <h4><i class="fas fa-bell"></i> Price Alerts</h4>
                <p>Set custom price alerts to be notified when a cryptocurrency reaches a specific price level or when a technical indicator signals a potential trade.</p>
            </div>
            <div class="feature-card">
                <h4><i class="fas fa-history"></i> Historical Backtesting</h4>
                <p>Test your trading strategies against historical data to see how they would have performed in past market conditions.</p>
            </div>
            <div class="feature-card">
                <h4><i class="fas fa-sync-alt"></i> Multi-Timeframe Analysis</h4>
                <p>Analyze price action across multiple timeframes simultaneously to identify stronger trading signals and confirm trends.</p>
            </div>
        </div>
    </div>

    <!-- Historical Data Modal -->
    <div id="historical-data-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">Historical On-Chain Data</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="modal-controls">
                    <div class="control-group">
                        <label for="metric-selector"><i class="fas fa-chart-line"></i> Metric</label>
                        <select id="metric-selector">
                            <option value="active_addresses">Active Addresses</option>
                            <option value="transaction_volume">Transaction Volume</option>
                            <option value="transaction_count">Transaction Count</option>
                            <option value="exchange_flow">Exchange Flow</option>
                            <option value="whale_holdings">Whale Holdings</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label for="timeframe-selector-modal"><i class="fas fa-calendar"></i> Timeframe</label>
                        <select id="timeframe-selector-modal">
                            <option value="7">7 Days</option>
                            <option value="30" selected>30 Days</option>
                            <option value="90">90 Days</option>
                            <option value="180">180 Days</option>
                        </select>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="historical-chart"></canvas>
                </div>
                <div class="analysis-container">
                    <h3>Advanced Analysis</h3>
                    <div class="analysis-grid">
                        <div class="analysis-card">
                            <h4>Correlation with Price</h4>
                            <div id="correlation-value">0.75 (Strong Positive)</div>
                            <div class="analysis-description">This metric has a strong positive correlation with price movements over the selected timeframe.</div>
                        </div>
                        <div class="analysis-card">
                            <h4>Trend Analysis</h4>
                            <div id="trend-value">Upward (Bullish)</div>
                            <div class="analysis-description">The metric shows a consistent upward trend, which is typically bullish for price.</div>
                        </div>
                        <div class="analysis-card">
                            <h4>Anomaly Detection</h4>
                            <div id="anomaly-value">No anomalies detected</div>
                            <div class="analysis-description">No unusual patterns or outliers detected in the recent data.</div>
                        </div>
                        <div class="analysis-card">
                            <h4>Predictive Power</h4>
                            <div id="predictive-value">High (85%)</div>
                            <div class="analysis-description">This metric has shown high predictive power for price movements in the past.</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://s3.tradingview.com/tv.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/coin-config.js"></script>
    <script src="js/coin-sync.js"></script>
    <script src="js/add-coin-sync.js"></script>
    <script src="js/data-service.js"></script>
    <script src="js/market-data.js"></script>
    <script src="js/cmc-data.js"></script>
    <script src="js/cc-data.js"></script>
    <script src="js/onchain-data.js"></script>
    <script src="js/trading-signals.js"></script>
    <script src="js/charts-page.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // TradingView chart is initialized by charts-page.js

            // Tab switching functionality
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons and contents
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Add active class to clicked button and corresponding content
                    this.classList.add('active');
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                });
            });

            // Timeframe switching functionality for trading signals
            const timeframeButtons = document.querySelectorAll('.timeframe-button');
            const signalsContainers = document.querySelectorAll('[id^="signals-"]');

            timeframeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all timeframe buttons
                    timeframeButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to clicked button
                    this.classList.add('active');

                    // Hide all signal containers
                    signalsContainers.forEach(container => {
                        container.style.display = 'none';
                    });

                    // Show the selected timeframe container
                    const timeframe = this.getAttribute('data-timeframe');
                    document.getElementById(`signals-${timeframe}`).style.display = 'grid';

                    // Update on-chain data for this timeframe
                    if (typeof onChainDataService !== 'undefined') {
                        onChainDataService.updateSignalsUI(timeframe);
                    }
                });
            });

            // Initialize on-chain data for the default timeframe (1h)
            setTimeout(() => {
                if (typeof onChainDataService !== 'undefined') {
                    onChainDataService.updateSignalsUI('1h');
                }

                // Initialize real market data
                if (typeof marketDataService !== 'undefined') {
                    marketDataService.updateAllPriceDisplays();
                }

                // Initialize CoinMarketCap data
                if (typeof cmcDataService !== 'undefined') {
                    cmcDataService.updateAllPriceDisplays();
                }

                // Initialize CryptoCompare data
                if (typeof ccDataService !== 'undefined') {
                    ccDataService.updateAllPriceDisplays();
                    ccDataService.updateAllTechnicalIndicatorsDisplays();
                }

                // Update market data every 60 seconds
                setInterval(() => {
                    if (typeof marketDataService !== 'undefined') {
                        marketDataService.updateAllPriceDisplays();
                    }
                    if (typeof cmcDataService !== 'undefined') {
                        cmcDataService.updateAllPriceDisplays();
                    }
                    if (typeof ccDataService !== 'undefined') {
                        ccDataService.updateAllPriceDisplays();
                    }
                }, 60000);

                // Update technical indicators every 5 minutes
                setInterval(() => {
                    if (typeof ccDataService !== 'undefined') {
                        ccDataService.updateAllTechnicalIndicatorsDisplays();
                    }
                }, 300000);
            }, 1000);

            // Update trading signals timestamp when tab is clicked
            const signalsTabButton = document.querySelector('[data-tab="signals-tab"]');
            if (signalsTabButton) {
                signalsTabButton.addEventListener('click', function() {
                    updateComponentTimestamp('trading-signals');
                });
            }

            // Update on-chain data timestamp when tab is clicked
            const onchainTabButton = document.querySelector('[data-tab="onchain-tab"]');
            if (onchainTabButton) {
                onchainTabButton.addEventListener('click', function() {
                    updateComponentTimestamp('onchain-data');
                    updateComponentTimestamp('onchain-chart-data');
                });
            }
        });
    </script>
    <!-- Trading Signals Module loaded above -->
</body>
</html>
